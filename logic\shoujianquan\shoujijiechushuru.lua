require "logic.dialog"

ShouJiJieChuShuRu = {}
setmetatable(ShouJiJieChuShuRu, Dialog)
ShouJiJieChuShuRu.__index = ShouJiJieChuShuRu

local _instance
function ShouJiJieChuShuRu.getInstance()
	if not _instance then
		_instance = ShouJiJieChuShuRu:new()
		_instance:OnCreate()
	end
	return _instance
end

function ShouJiJieChuShuRu.getInstanceAndShow()
	if not _instance then
		_instance = ShouJiJieChuShuRu:new()
		_instance:OnCreate()
	else
		_instance:SetVisible(true)
	end
	return _instance
end

function ShouJiJieChuShuRu.getInstanceNotCreate()
	return _instance
end

function ShouJiJieChuShuRu.DestroyDialog()
	if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
end

function ShouJiJieChuShuRu.ToggleOpenClose()
	if not _instance then
		_instance = ShouJiJieChuShuRu:new()
		_instance:OnCreate()
	else
		if _instance:IsVisible() then
			_instance:SetVisible(false)
		else
			_instance:SetVisible(true)
		end
	end
end

function ShouJiJieChuShuRu.GetLayoutFileName()
	return "shoujijiechushuru.layout"
end

function ShouJiJieChuShuRu:new()
	local self = {}
	self = Dialog:new()
	setmetatable(self, ShouJiJieChuShuRu)
	return self
end

function ShouJiJieChuShuRu:OnCreate()
	Dialog.OnCreate(self)
	local winMgr = CEGUI.WindowManager:getSingleton()

	self.m_btn_send = CEGUI.toPushButton(winMgr:getWindow("shoujijiechushuru/btn"))
    self.m_btn_send:subscribeEvent("Clicked", ShouJiJieChuShuRu.OnBtnSendClicked, self)

	self.m_closeBtn = CEGUI.toPushButton(winMgr:getWindow("shoujijiechushuru/guanbi"))
    self.m_closeBtn:subscribeEvent("Clicked", ShouJiJieChuShuRu.DestroyDialog, nil)

	self.m_text_tel = CEGUI.toRichEditbox(winMgr:getWindow("shoujijiechushuru/shurukuang/shuru"))
    self.m_text_tel:subscribeEvent("KeyboardTargetWndChanged", ShouJiJieChuShuRu.Tel_OnKeyboardTargetWndChanged, self)
    self.m_text_tel:setMaxTextLength(11)
    self.m_text_tel:SetColourRect(CEGUI.PropertyHelper:stringToColour(self.m_text_tel:getProperty("NormalTextColour")))
    self.tel_placeHolder = winMgr:getWindow("shoujijiechushuru/text23")

    local shoujianquanmgr = require "logic.shoujianquan.shoujianquanmgr"
    local strTel = tostring(shoujianquanmgr.tel)
    strTel = string.sub(strTel, 1, 3) .. "****" .. string.sub(strTel, 8)
    local strText = MHSD_UTILS.get_resstring(11661)
    local sb = StringBuilder:new()
    sb:Set("parameter1", strTel)
    strText = sb:GetString(strText)
    sb:delete()

	self.m_desc = CEGUI.toRichEditbox(winMgr:getWindow("shoujijiechushuru/box"))
    self.m_desc:Clear()
    self.m_desc:AppendParseText(CEGUI.String(strText))
    self.m_desc:Refresh()
end

--失去输入焦点
function ShouJiJieChuShuRu:Tel_OnKeyboardTargetWndChanged(args)
    local wnd = CEGUI.toWindowEventArgs(args).window
    if wnd == self.m_text_tel then
        self.tel_placeHolder:setVisible(false)
    else
        if self.m_text_tel:GetPureText() == "" then
            self.tel_placeHolder:setVisible(true)
        end
    end
end


function ShouJiJieChuShuRu:BeginCountDown()
    local shoujianquanmgr = require "logic.shoujianquan.shoujianquanmgr"
    if not self.m_bCountDown then
        self.m_bCountDown = true
        self.m_nCountDownLife = shoujianquanmgr.finishtimepoint - gGetServerTime()
        self.m_btn_requestcode:setEnabled(false)
        self:ShowCountDown()
        self.m_text_tel:setReadOnly(true)
    end
end

function ShouJiJieChuShuRu:CountDownUpdate(delta)
    if self.m_bCountDown then
        self.m_nCountDownLife = self.m_nCountDownLife - delta
        if self.m_nCountDownLife < 0 then
            self.m_nCountDownLife = 0

            self.m_bCountDown = false
            self.m_nCountDownLife = 0
            self.m_btn_requestcode:setEnabled(true)
            self.m_btn_requestcode:setText(self.m_strRequestCode)
            self.m_text_tel:setReadOnly(false)
        else
            self:ShowCountDown()
        end
    end
end

function ShouJiJieChuShuRu:ShowCountDown()
    local remainSecond = math.floor(self.m_nCountDownLife / 1000) + 1
    local strCountDown = self.m_strRequestCode .. "(" .. remainSecond .. ")"
    self.m_btn_requestcode:setText(strCountDown)
end
local lastClickTime = 0
function ShouJiJieChuShuRu:OnBtnSendClicked()
    local shoujianquanmgr = require "logic.shoujianquan.shoujianquanmgr"

    local strTel = self.m_text_tel:GetPureText()
    local nTel = tonumber(strTel)
    if nTel == nil or string.len(strTel) ~= 11 or nTel ~= shoujianquanmgr.tel then
		GetCTipsManager():AddMessageTipById(191006)
        return
    end

    local p = require("protodef.fire.pb.cunbindtel"):new()
    p.tel = nTel
    p.code = "123456"
    LuaProtocolManager:send(p)
end

return ShouJiJieChuShuRu