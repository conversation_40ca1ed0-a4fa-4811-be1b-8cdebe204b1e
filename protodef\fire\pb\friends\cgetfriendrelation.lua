require "utils.tableutil"
CGetFriendRelation = {}
CGetFriendRelation.__index = CGetFriendRelation



CGetFriendRelation.PROTOCOL_TYPE = 817944

function CGetFriendRelation.Create()
	print("enter CGetFriendRelation create")
	return CGetFriendRelation:new()
end
function CGetFriendRelation:new()
	local self = {}
	setmetatable(self, CGetFriendRelation)
	self.type = self.PROTOCOL_TYPE
	self.roleid = 0

	return self
end
function CGetFriendRelation:encode()
	local os = FireNet.Marshal.OctetsStream:new()
	os:compact_uint32(self.type)
	local pos = self:marshal(nil)
	os:marshal_octets(pos:getdata())
	pos:delete()
	return os
end
function CGetFriendRelation:marshal(ostream)
	local _os_ = ostream or FireNet.Marshal.OctetsStream:new()
	_os_:marshal_int64(self.roleid)
	return _os_
end

function CGetFriendRelation:unmarshal(_os_)
	self.roleid = _os_:unmarshal_int64()
	return _os_
end

return CGetFriendRelation
