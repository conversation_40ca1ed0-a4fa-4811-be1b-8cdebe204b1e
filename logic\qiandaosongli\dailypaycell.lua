DailyPayCell = {}

setmetatable(DailyPayCell, Dialog)
DailyPayCell.__index = DailyPayCell
local prefix = 0

function DailyPayCell.CreateNewDlg(parent)
	local newDlg = DailyPayCell:new()
	newDlg:OnCreate(parent)
	return newDlg
end

function DailyPayCell.GetLayoutFileName()
	return "dailypaycell.layout"
end

function DailyPayCell:new()
	local self = {}
	self = Dialog:new()
	setmetatable(self, DailyPayCell)
	return self
end

function DailyPayCell:OnCreate(parent)
	prefix = prefix + 1
	Dialog.OnCreate(self, parent, prefix)

	local winMgr = CEGUI.WindowManager:getSingleton()
	local prefixstr = tostring(prefix)

	self.m_bg = winMgr:getWindow(prefixstr .. "dailypaycell")
	self.m_itemcellItemA = CEGUI.toItemCell(winMgr:getWindow(prefixstr .. "dailypaycell/daoju1"))
	self.m_itemcellItemB = CEGUI.toItemCell(winMgr:getWindow(prefixstr .. "dailypaycell/daoju2"))
	self.m_itemcellItemC = CEGUI.toItemCell(winMgr:getWindow(prefixstr .. "dailypaycell/daoju3"))

    self.vItemCell = {}
    self.vItemCell[#self.vItemCell +1] = self.m_itemcellItemA
    self.vItemCell[#self.vItemCell +1] = self.m_itemcellItemB
    self.vItemCell[#self.vItemCell +1] = self.m_itemcellItemC

	--self.m_imgDay = winMgr:getWindow(prefixstr .."dailypaycell/tu")
    self.tips = winMgr:getWindow(prefixstr .."dailypaycell/tian")
		
	self.m_btnGot = CEGUI.toPushButton(winMgr:getWindow(prefixstr .. "dailypaycell/lingqu"))
	self.m_btnGot:subscribeEvent("Clicked",DailyPayCell.HandleGotBtnClick,self)
	self.m_imgGot = winMgr:getWindow(prefixstr .. "dailypaycell/lingqu/yilingqu")
	
	for i = 1 , 3 do
		local n =  CEGUI.toItemCell(winMgr:getWindow(prefixstr .. "dailypaycell/daoju"..i))
		n:setID( i )
		n:subscribeEvent("MouseClick",DailyPayCell.HandleItemClicked,self)
	end
	
	self.m_id = 0
	self.m_flag = -1
end

function DailyPayCell:SetID( id )
	self.m_id = id	
end

function DailyPayCell:SetFlag( bFlag )
	self.m_flag = bFlag	
end

function DailyPayCell:getArrayItem(vItemId,nPayLv)
	local itemDayTable = BeanConfigManager.getInstance():GetTableByName("game.cdailypay"):getRecorder(nPayLv)
    if not itemDayTable then
        return
    end
    local nIndex = #vItemId +1
    vItemId[nIndex] = {}
    local nItemId1,nItemNum1 = self:getItemIdAndNumWithIdAndNum(itemDayTable.item1id, itemDayTable.item1num)
    vItemId[nIndex].nItemId = nItemId1
    vItemId[nIndex].nItemNum = nItemNum1

    nIndex = #vItemId +1
    vItemId[nIndex] = {}
    local nItemId2,nItemNum2 = self:getItemIdAndNumWithIdAndNum(itemDayTable.item2id, itemDayTable.item2num)
    vItemId[nIndex].nItemId = nItemId2
    vItemId[nIndex].nItemNum = nItemNum2
   
    nIndex = #vItemId +1
    vItemId[nIndex] = {}
    local nItemId3,nItemNum3 = self:getItemIdAndNumWithIdAndNum(itemDayTable.item3id, itemDayTable.item3num)
    vItemId[nIndex].nItemId = nItemId3
    vItemId[nIndex].nItemNum = nItemNum3
end

function DailyPayCell:getItemIdAndNumWithIdAndNum(vcItemId, vcItemNum)
    
    local nItemId = vcItemId
    local nItemNum = vcItemNum
    return nItemId,nItemNum
end


function DailyPayCell:RefreshShow()
	local parentTimes = DailyPayReward.getInstanceNotCreate().m_continueDays
	
	self.m_imgGot:setVisible(false)
	self.m_btnGot:setVisible( true )
	
	if  self.m_id <=  parentTimes then 
		if self.m_flag == 0 then
			self.m_btnGot:setEnabled( true )
		elseif self.m_flag > 0  then
			self.m_btnGot:setVisible( false )
			self.m_imgGot:setVisible( true )			
		end
	else
		self.m_btnGot:setVisible( true )
		self.m_btnGot:setEnabled( false )
	end
    local itemDayTable = BeanConfigManager.getInstance():GetTableByName("game.cdailypay"):getRecorder( self.m_id )
    if not itemDayTable then
        return
    end

	local vItemId = {}
    self:getArrayItem(vItemId, self.m_id)
    for nIndex=1, #vItemId do
        local oneData = vItemId[nIndex]
        local nItemId = oneData.nItemId
        local nItemNum = oneData.nItemNum
        local itemTable = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(nItemId)
        local itemCellNode = self.vItemCell[nIndex]
        if itemTable then
            itemCellNode:SetImage(gGetIconManager():GetItemIconByID( itemTable.icon ))
            SetItemCellBoundColorByQulityItemWithId(itemCellNode,nItemId)
            if nItemNum> 1 then
                itemCellNode:SetTextUnitText(CEGUI.String(""..nItemNum))
            end
	         refreshItemCellTypeForHuoban(itemCellNode,nItemId)
             itemCellNode:setVisible(true)
             itemCellNode:setID(nItemId)
        else
            itemCellNode:setVisible(false)
        end
    end
    self.tips:setText(itemDayTable.money)
	--self.m_imgDay:setProperty("Image",  itemDayTable.dayimg)
end

function DailyPayCell:HandleItemClicked(args)

	local e = CEGUI.toWindowEventArgs(args)
	local nItemId = e.window:getID()
	local e = CEGUI.toMouseEventArgs(args)
	local touchPos = e.position
	
	local itemAttrCfg = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(nItemId)
	if not itemAttrCfg then
        if BeanConfigManager.getInstance():GetTableByName("pet.cpetattr"):getRecorder(nItemId) then
            require "logic.pet.firstchargegiftpetdlg".getInstanceAndShow(nItemId)
        end
        return
	end

	local nPosX = touchPos.x
	local nPosY = touchPos.y
	local Commontipdlg = require "logic.tips.commontipdlg"
	local commontipdlg = Commontipdlg.getInstanceAndShow()
	local nType = Commontipdlg.eType.eNormal 
	commontipdlg:RefreshItem(nType,nItemId,nPosX,nPosY)
end

function DailyPayCell:HandleGotBtnClick(args)
	local p = require "protodef.fire.pb.item.cgetdailypaygift":new()
	p.rewardid = self.m_id
	require "manager.luaprotocolmanager":send(p)
end

return DailyPayCell
