require "logic.dialog"

PetShiPing = {}
setmetatable(PetShiPing, Dialog)
PetShiPing.__index = PetShiPing

local _instance
function PetShiPing.getInstance()
	if not _instance then
		_instance = PetShiPing:new()
		_instance:OnCreate()
	end
	return _instance
end

function PetShiPing.getInstanceAndShow()
	if not _instance then
		_instance = PetShiPing:new()
		_instance:OnCreate()
	else
		_instance:SetVisible(true)
	end
	return _instance
end

function PetShiPing.getInstanceNotCreate()
	return _instance
end

function PetShiPing.DestroyDialog()
	if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
end

function PetShiPing.ToggleOpenClose()
	if not _instance then
		_instance = PetShiPing:new()
		_instance:OnCreate()
	else
		if _instance:IsVisible() then
			_instance:SetVisible(false)
		else
			_instance:SetVisible(true)
		end
	end
end

function PetShiPing.GetLayoutFileName()
	return "petshipin.layout"
end

function PetShiPing:new()
	local self = {}
	self = Dialog:new()
	setmetatable(self, PetShiPing)
	return self
end
function PetShiPing:setPetKey( petKey )
    self.petKey = petKey
end
function PetShiPing:OnCreate()
	Dialog.OnCreate(self)
	local winMgr = CEGUI.WindowManager:getSingleton()
	local ts1 = BeanConfigManager.getInstance():GetTableByName("pet.Ctisheng"):getRecorder(8)
	self.frameWindow = CEGUI.toFrameWindow(winMgr:getWindow("petshipin/framewindow"))
	self.quxiaoBtn = CEGUI.toPushButton(winMgr:getWindow("petshipin/quxiao"))
	self.querenBtn = CEGUI.toPushButton(winMgr:getWindow("petshipin/queren"))
	self.ItemCellNeedItem1 = CEGUI.toItemCell(winMgr:getWindow("petshipin/framewindow/needitem"))
	self.describe= winMgr:getWindow("petshipin/textewai")
	self.describe:setText(tostring("宠物血脉一级增加"..ts1.addnum..ts1.shuoming))
	self.needNumText = winMgr:getWindow("petshipin/textqian")
	self.needNumText:setText(formatMoneyString(ts1.needmoney))
	self.needitemText = winMgr:getWindow("petshipin/textqian1")
	self.itemnum = winMgr:getWindow("petshipin/textqian11")
	local itemInfo = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(ts1.needitemid)
	self.needitemText:setText(ts1.needitemidnum.."个"..itemInfo.name)
	self.ItemCellNeedItem1:SetImage(gGetIconManager():GetItemIconByID(itemInfo.icon))
	local roleItemManager = require("logic.item.roleitemmanager").getInstance()
	local num = roleItemManager:GetItemNumByBaseID(ts1.needitemid)
	self.itemnum:setText(num.."个"..itemInfo.name)
	self.quxiaoBtn:subscribeEvent("Clicked", PetShiPing.handleUseGoldClicked, self)
	self.querenBtn:subscribeEvent("Clicked", PetShiPing.handleUseStoneClicked, self)
	self.frameWindow:getCloseButton():subscribeEvent("Clicked", PetShiPing.handleUseGoldClicked, self)
	

end

function PetShiPing:handleUseGoldClicked(args)
  PetShiPing:DestroyDialog()

end

function PetShiPing:handleUseStoneClicked(args)
if self.petKey then
	    if self.petKey >0 then
            local p = require "protodef.fire.pb.pet.cpetshipin".new()
		    p.petkey = self.petKey
			p.count = 8
		    p.needmoney = GameTable.common.GetCCommonTableInstance():getRecorder(486).value
		    require "manager.luaprotocolmanager":send(p)
        end
    end
    PetShiPing.DestroyDialog()
    return true
end

return PetShiPing