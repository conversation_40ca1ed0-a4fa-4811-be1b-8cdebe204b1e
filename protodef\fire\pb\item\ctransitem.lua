require "utils.tableutil"
CTransItem = {}
CTransItem.__index = CTransItem



CTransItem.PROTOCOL_TYPE = 787447

function CTransItem.Create()
	print("enter CTransItem create")
	return CTransItem:new()
end
function CTransItem:new()
	local self = {}
	setmetatable(self, CTransItem)
	self.type = self.PROTOCOL_TYPE
	self.srcpackid = 0
	self.srckey = 0
	self.number = 0
	self.dstpackid = 0
	self.dstpos = 0
	self.page = 0
	self.npcid = 0

	return self
end
function CTransItem:encode()
	local os = FireNet.Marshal.OctetsStream:new()
	os:compact_uint32(self.type)
	local pos = self:marshal(nil)
	os:marshal_octets(pos:getdata())
	pos:delete()
	return os
end
function CTransItem:marshal(ostream)
	local _os_ = ostream or FireNet.Marshal.OctetsStream:new()
	_os_:marshal_int32(self.srcpackid)
	_os_:marshal_int32(self.srckey)
	_os_:marshal_int32(self.number)
	_os_:marshal_int32(self.dstpackid)
	_os_:marshal_int32(self.dstpos)
	_os_:marshal_int32(self.page)
	_os_:marshal_int64(self.npcid)
	return _os_
end

function CTransItem:unmarshal(_os_)
	self.srcpackid = _os_:unmarshal_int32()
	self.srckey = _os_:unmarshal_int32()
	self.number = _os_:unmarshal_int32()
	self.dstpackid = _os_:unmarshal_int32()
	self.dstpos = _os_:unmarshal_int32()
	self.page = _os_:unmarshal_int32()
	self.npcid = _os_:unmarshal_int64()
	return _os_
end

return CTransItem
