require "logic.qiandaosongli.loginrewardmanager"

DailyPayReward = {}
DailyPayReward.__index = DailyPayReward

local _instance

function DailyPayReward.create()
    if not _instance then
		_instance = DailyPayReward:new()
		_instance:OnCreate()
	end
	return _instance
end

function DailyPayReward.getInstance()
    local Jianglinew = require("logic.qiandaosongli.jianglinewdlg")
    local jlDlg = Jianglinew.getInstanceAndShow()
     if not jlDlg then
        return nil
    end 
    local dlg = jlDlg:showSysId(Jianglinew.systemId.sevenSign)
	return dlg
end

function DailyPayReward.getInstanceAndShow()
	return DailyPayReward.getInstance()
end

function DailyPayReward.getInstanceNotCreate()
	return _instance
end

function DailyPayReward:remove()
    self:clearData()
    _instance = nil
end

function DailyPayReward:clearData()
    
    if not self.m_listCell then
        return
    end

    for i,v in pairs(self.m_listCell) do
		v:OnClose()
	end

end

function DailyPayReward.DestroyDialog()
     require("logic.qiandaosongli.jianglinewdlg").DestroyDialog()
end

function DailyPayReward:new()
	local self = {}
	setmetatable(self, DailyPayReward)
	return self
end

function DailyPayReward:OnCreate(parent)
    self.parent = parent
	local winMgr = CEGUI.WindowManager:getSingleton()
	local layoutName = "dailypay.layout"
	self.m_pMainFrame = winMgr:loadWindowLayout(layoutName)
	self.m_bg = CEGUI.toFrameWindow(winMgr:getWindow("dailypay"))
	self.m_scrollCellScro = CEGUI.toScrollablePane(winMgr:getWindow("dailypay/di1/liebiao"))
	self.m_txtTotalText = winMgr:getWindow("dailypay/di/num")
	self.m_txtTotalText:setText(tostring(LoginRewardManager.getInstance():GetDangRiPay().."元"))
	self.m_continueDays = LoginRewardManager.getInstance():GetRewardLv()
	self.m_gotList = {}
	self.m_gotList = LoginRewardManager.getInstance():GetMeiRiRewardMap()
	self.elapse = 0
	self.m_nIntroAID = 11187
	self.m_listCell = {}
	self:InitCell()

end

function DailyPayReward:update(delta)
	self.elapse = self.elapse+delta
	if self.elapse > 3000 then
		self.m_nIntroAID = self.m_nIntroAID + 1
		if self.m_nIntroAID > 11189 then
			self.m_nIntroAID = 11187
		end
		self.elapse = 0
	end
end

function DailyPayReward:InitCell()
	local itemDayTable = BeanConfigManager.getInstance():GetTableByName("game.cdailypay"):getAllID()
	for i = 1 , #itemDayTable do
        local cellclass =  require "logic.qiandaosongli.dailypaycell"
		local cell = cellclass.CreateNewDlg(self.m_scrollCellScro)
		local x = CEGUI.UDim(0, 5)
		local y = CEGUI.UDim(0, cell:GetWindow():getPixelSize().height*(i-1) + (i - 1)*8)
		local pos = CEGUI.UVector2(x,y)
		cell:GetWindow():setPosition(pos)
		cell:SetID(i)
		if self.m_gotList[i] then
			cell:SetFlag( self.m_gotList[i] )
		end
		cell:RefreshShow()
		table.insert(self.m_listCell, cell )
	end	
	
end

function DailyPayReward:UpdateCellData()
	self.m_gotList = LoginRewardManager.getInstance():GetMeiRiRewardMap()
    self.m_continueDays = LoginRewardManager.getInstance():GetRewardLv()
	self.m_txtTotalText:setText(tostring(LoginRewardManager.getInstance():GetDangRiPay().."仙玉"))
	for i , v in pairs( self.m_listCell ) do
		
		if self.m_gotList[i] then
			v:SetFlag( self.m_gotList[i] )
		end
		v:RefreshShow()
	end
end

return DailyPayReward
