require "logic.dialog"

ChaKan = { }
setmetatable(<PERSON><PERSON><PERSON>, <PERSON>alog)
ChaKan.__index = ChaKan

local _instance
function ChaKan.getInstance()
    if not _instance then
        _instance = ChaKan:new()
        _instance:OnCreate()
    end
    return _instance
end

function ChaKan.getInstanceAndShow()
    if not _instance then
        _instance = ChaKan:new()
        _instance:OnCreate()
    else
        _instance:SetVisible(true)
    end
    return _instance
end

function ChaKan.getInstanceNotCreate()
    return _instance
end

function ChaKan.DestroyDialog()
    if _instance then
        if not _instance.m_bCloseIsHide then
            _instance:OnClose()
            _instance = nil
        else
            _instance:ToggleOpenClose()
        end
    end
end

function ChaKan.ToggleOpenClose()
    if not _instance then
        _instance = ChaKan:new()
        _instance:OnCreate()
    else
        if _instance:IsVisible() then
            _instance:SetVisible(false)
        else
            _instance:SetVisible(true)
        end
    end
end

function ChaKan.GetLayoutFileName()
    return "chakan_mtg.layout"
end

function ChaKan:new()
    local self = { }
    self = Dialog:new()
    setmetatable(self, <PERSON><PERSON><PERSON>)
    return self
end

function ChaKan:OnCreate()
    Dialog.OnCreate(self)
    local winMgr = CEGUI.WindowManager:getSingleton()

    self.m_ViewPetBtn = CEGUI.toPushButton(winMgr:getWindow("chakan_mtg/btn1"))
    self.m_yuanshenBtn = CEGUI.toPushButton(winMgr:getWindow("chakan_mtg/btn11"))
    self.m_Equip1 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/tou"))
    self.m_Equip2 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/wuqi"))
    self.m_Equip3 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/yaodai"))
    self.m_Equip4 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/Cloak"))
    self.m_Equip5 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/xianglian"))
    self.m_Equip6 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/kaijia"))
    self.m_Equip7 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/xiezi"))
    self.m_Equip8 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/extern01"))
    self.m_Equip9 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/extern02"))
    self.m_Equip10 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/extern03"))
    self.m_Equip11 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/extern04"))
    self.m_Equip12 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/extern05"))
    self.m_Equip13 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/extern06"))
    self.m_Equip14 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/extern07"))
    self.m_Equip15 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/extern08"))
    self.m_Equip16 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/extern09"))
    self.m_Equip17 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/extern10"))
    self.m_Equip18 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/extern11"))
    self.m_Equip19 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/extern12"))
    self.m_Equip20 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/extern13"))
    self.m_Equip21 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/extern14"))
    self.m_Equip22 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/extern15"))
    self.m_Equip23 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/extern16"))
    self.m_Equip24 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/extern17"))

    self.m_Equip25 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/extern18"))
    self.m_Equip26 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/extern19"))
    self.m_Equip27 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/extern20"))
    self.m_Equip28 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/extern21"))
    self.m_Equip29 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/extern22"))
    self.m_Equip30 = CEGUI.toItemCell(winMgr:getWindow("chakan_mtg/extern23"))
    self.m_ZhiYe = winMgr:getWindow("chakan_mtg/ditu/name1")
    self.m_Score = winMgr:getWindow("chakan_mtg/ditu/pingfen1")
    self.m_Close = CEGUI.toPushButton(winMgr:getWindow("chakan_mtg/X"))
    self.m_Close:subscribeEvent("Clicked", ChaKan.OnClickedClose, self)
    self.m_ViewPetBtn:subscribeEvent("Clicked", ChaKan.OnClickedViewPet, self)
    self.m_yuanshenBtn:subscribeEvent("Clicked", ChaKan.OnClickedViewYuanShen, self)
    self.SpriteWnd = winMgr:getWindow("chakan_mtg/ditu/yinying")
    self.m_ZhiYeIcon = winMgr:getWindow("chakan_mtg/ditu/name")
    self.m_LevelText  = winMgr:getWindow("chakan_mtg/ditu/dengji2")
    

    self.PosTable =
    {
        [0] = self.m_Equip2,
        [1] = nil,
        [2] = self.m_Equip5,
        [3] = self.m_Equip6,
        [4] = self.m_Equip3,
        [5] = self.m_Equip7,
        [6] = self.m_Equip1,
        [7] = self.m_Equip4,
        [8] = self.m_Equip8,
        [9] = self.m_Equip9,
        [10] =self.m_Equip10,
        [11] = self.m_Equip11,
        [12] = self.m_Equip12,
        [13] = self.m_Equip13,
        [14] = self.m_Equip14,
        [15] = self.m_Equip15,
        [16] = self.m_Equip16,
        [17] = self.m_Equip17,
        [18] = self.m_Equip18,
        [19] = self.m_Equip19,
        [20] = self.m_Equip20,
        [21] = self.m_Equip21,
        [22] = self.m_Equip22,
        [23] = self.m_Equip23,
        [24] = self.m_Equip24,

        [25] =  self.m_Equip25,
        [26] =  self.m_Equip26,
        [27] =  self.m_Equip27,
        [28] =   self.m_Equip28,
        [29] =  self.m_Equip29,
        [30] =  self.m_Equip30,
    }
   
   self:GetWindow():setRiseOnClickEnabled(false)
   self:setShiZhuangOpenStatus(false)

end
function ChaKan:OnClickedViewPet(args)
    local p = require("protodef.fire.pb.item.cgetrolepetinfo"):new()
    p.roleid = self.m_Data.roleid
    LuaProtocolManager:send(p)
end

function ChaKan:setShiZhuangOpenStatus(isVisible)
	if isVisible ~= nil then
		self.isOpen  = isVisible
		self.isOpen1  = true
	else
		if self.isOpen == nil then 
			self.isOpen = false
			self.isOpen1  = true
		elseif self.isOpen  == false then
			self.isOpen = true
			self.isOpen1  = false
		elseif self.isOpen  == true then
			self.isOpen = false
			self.isOpen1  = true
		end
	end
    print("查看",self.isOpen,self.isOpen1)
    self.m_Equip1:setVisible(self.isOpen1)

    self.m_Equip2:setVisible(self.isOpen1)
    self.m_Equip3:setVisible(self.isOpen1)
    self.m_Equip4:setVisible(self.isOpen1)
    self.m_Equip5:setVisible(self.isOpen1)
    self.m_Equip6:setVisible(self.isOpen1)
    self.m_Equip7:setVisible(self.isOpen1)
    self.m_Equip8:setVisible(self.isOpen1)
    self.m_Equip9:setVisible(self.isOpen1)
    self.m_Equip10:setVisible(self.isOpen1)
    self.m_Equip11:setVisible(self.isOpen1)
    self.m_Equip12:setVisible(self.isOpen1)
    self.m_Equip13:setVisible(self.isOpen1)
    self.m_Equip14:setVisible(self.isOpen1)
    self.m_Equip15:setVisible(self.isOpen1)
    self.m_Equip16:setVisible(self.isOpen1)
    self.m_Equip17:setVisible(self.isOpen1)
    self.m_Equip18:setVisible(self.isOpen1)
    self.m_Equip19:setVisible(self.isOpen1)
    self.m_Equip20:setVisible(self.isOpen1)
    self.m_Equip21:setVisible(self.isOpen1)
    self.m_Equip22:setVisible(self.isOpen1)
    self.m_Equip23:setVisible(self.isOpen1)
    self.m_Equip24:setVisible(self.isOpen1)

    self.m_Equip25:setVisible(self.isOpen)
    self.m_Equip26:setVisible(self.isOpen)
    self.m_Equip27:setVisible(self.isOpen)
    self.m_Equip28:setVisible(self.isOpen)
    self.m_Equip29:setVisible(self.isOpen)
    self.m_Equip30:setVisible(self.isOpen)
end
function ChaKan:OnClickedViewYuanShen()
	self:setShiZhuangOpenStatus()
end
-- ���cell
function ChaKan:HandleCellClicked(args)
    local id = CEGUI.toWindowEventArgs(args).window:getID()
    local id2 = CEGUI.toWindowEventArgs(args).window:getID2()
    local p = require("protodef.fire.pb.item.cotheritemtips"):new()
    p.roleid = self.m_Data.roleid
    p.packid = fire.pb.item.BagTypes.EQUIP
    p.keyinpack = id
    LuaProtocolManager:send(p)

    local pos = CEGUI.toWindowEventArgs(args).window:GetScreenPosOfCenter()
    local roleItem = RoleItem:new()
    roleItem:SetItemBaseData(id2, 0)
    roleItem:SetObjectID(id2)
    local tip = Commontiphelper.showItemTip(id2, roleItem:GetObject(), true, false, pos.x, pos.y)
    tip.roleid = self.m_Data.roleid
    tip.roleItem = roleItem
    tip.itemkey = id

end

function ChaKan:OnClickedClose(args)
    self:DestroyDialog()
end

function ChaKan:checkEquipEffect(roleSprite, quality)
    if quality ~= 0 then
        if quality>10 then
			quality= 10
		end
        local record = BeanConfigManager.getInstance():GetTableByName("role.cequipeffectconfig"):getRecorder(quality)
        if record.effectId ~= 0 then
            self.m_pPackEquipEffect = roleSprite:SetEngineSpriteDurativeEffect(MHSD_UTILS.get_effectpath(record.effectId), false);
            if self.m_pPackEquipEffect then
                self.m_pPackEquipEffect:SetScale(1,1)
            end
        end
    end
end

function ChaKan:RefreshData(prag)
    -- ����ֵ
    self.m_Data = prag
    if not self.m_Data then
        return
    end
    if not self.m_Data.equipinfo then
        return
    end
    local equips = self.m_Data.equipinfo.items
    -- ˢ��װ����ʾ
    for k, v in pairs(equips) do
        if v then
            local info = self.PosTable[v.position]
            if info then
                -- ����ͼƬ
                local itemattr = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(v.id)
                if itemattr then
                    info:SetImage(gGetIconManager():GetItemIconByID(itemattr.icon))
                    info:setID(v.key)
                    info:setID2(v.id)
                    SetItemCellBoundColorByQulityItem(info, itemattr.nquality)
                    info:subscribeEvent("TableClick", self.HandleCellClicked, self)
                end
            end
        end
    end


    -- ����
    if self.SpriteWnd then
        local shape = 0
        if self.m_Data.shape > 20 then
         shape=self.m_Data.shape
        else
        shape=self.m_Data.shape 
        end
        local roleSprite = gGetGameUIManager():AddWindowSprite(self.SpriteWnd, shape, Nuclear.XPDIR_BOTTOM, 0, 0, false)
       -- roleSprite:SetUIScale(1.5)
        for i, v in pairs(self.m_Data.components) do
        if i ~= eSprite_WeaponColor and i ~= eSprite_Fashion
			and i ~= eSprite_DyePartA and i ~= eSprite_DyePartB then
            roleSprite:SetSpriteComponent(i, v)
        elseif 50 <= i and i <= 59 then
            roleSprite:SetDyePartIndex(i-50,v)
        elseif i == eSprite_Weapon then
            roleSprite:UpdateWeaponColorParticle(v)
        end
        if self.m_Data.components[60] then -- ��װ��Ч
            self:checkEquipEffect(roleSprite, self.m_Data.components[60])
        end
    end
    end

    -- �ۺ�����
    if self.m_Score then
        self.m_Score:setText(tostring(self.m_Data.totalscore))
    end

    -- ����
    if self.m_ZhiYe then
        self.m_ZhiYe:setText(tostring(self.m_Data.rolename))
    end

    if self.m_ZhiYeIcon then
        -- ְҵ
        local schoolName = BeanConfigManager.getInstance():GetTableByName("role.schoolinfo"):getRecorder(self.m_Data.profession)
        if schoolName then
            self.m_ZhiYeIcon:setProperty("Image", schoolName.schooliconpath)
        end
    end

   if self.m_LevelText then
    self.m_LevelText:setText(tostring(self.m_Data.rolelevel))
    end

end



return ChaKan
