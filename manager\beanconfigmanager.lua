
--[[
================================================================================
Bean配置管理器
================================================================================
文件名: beanconfigmanager.lua
功能: 管理游戏配置表的加载和访问，支持XML和二进制格式的配置文件
作者: 大唐游戏开发团队
创建时间: 未知
最后修改: 未知
================================================================================
]]--

-- Bean配置管理器类定义
BeanConfigManager = {}
BeanConfigManager.__index = BeanConfigManager

-- 单例实例
local _instance

--[[
获取Bean配置管理器单例实例
返回值: BeanConfigManager - 管理器实例
]]--
function BeanConfigManager.getInstance()
    if not _instance then
        _instance = BeanConfigManager:new()
    end
    return _instance
end

--[[
移除单例实例
]]--
function BeanConfigManager.removeInstance()
	_instance = nil
end

--[[
初始化配置管理器
参数:
  xmlpath - XML配置文件路径
  binpath - 二进制配置文件路径
]]--
function BeanConfigManager:Initialize(xmlpath, binpath)
	self.m_xmlPath = xmlpath
	self.m_binPath = binpath

    -- 预加载重要配置表
    -- 有时在SDK登录期间无法获取记录器，在此处预加载
    self:GetTableByName("message.cstringres")
end

--[[
创建新的Bean配置管理器实例
返回值: BeanConfigManager - 新实例
]]--
function BeanConfigManager:new()
    local self = {}
    setmetatable(self, BeanConfigManager)

	self.m_xmlPath = ""          -- XML文件路径
	self.m_tableInstance = {}    -- 配置表实例缓存
    return self
end

--[[
创建配置表实例
参数: tablename - 配置表名称
返回值: boolean - 是否加载成功
]]--
function BeanConfigManager:MakeTableValue(tablename)
	local xmlfilename  = self.m_xmlPath .. tablename .. ".xml"
	local binfilename  = self.m_binPath .. tablename .. ".bin"

	-- 加载配置表定义模块
	local mod = require("tabledef." .. tablename)
	if not mod then
		print('[LUA ERROR] table name is error: '..tablename)
		LogErr('[LUA ERROR] table name is error: '..tablename)
		return
	end

	-- 创建配置表实例并从二进制文件加载数据
	self.m_tableInstance[tablename] = mod:new()
	return self.m_tableInstance[tablename]:LoadBeanFromBinFile(binfilename)
end

--[[
根据名称获取配置表实例
参数: tablename - 配置表名称 (例如: "item.itemattr", "item.CItemAttr")
返回值: table - 配置表实例
]]--
function BeanConfigManager:GetTableByName(tablename)
    -- 修正配置表文件路径
    tablename = g_getCorrectTableFilePath(tablename)
    tablename = g_CheckSimpTableName(tablename)

	-- 如果配置表实例不存在，则创建它
	if not self.m_tableInstance[tablename] then
		self:MakeTableValue(tablename)
	end
	return self.m_tableInstance[tablename]
end

return BeanConfigManager
