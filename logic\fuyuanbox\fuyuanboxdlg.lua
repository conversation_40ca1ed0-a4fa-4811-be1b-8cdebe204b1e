require "logic.dialog"

fuyuanBoxDlg = {}
setmetatable(fuyuanBoxDlg, Dialog)
fuyuanBoxDlg.__index = fuyuanBoxDlg

local _instance
local m_ClickStart = false
function fuyuanBoxDlg.getInstance()
	if not _instance then
		_instance = fuyuanBoxDlg:new()
		_instance:OnCreate()
	end
	return _instance
end

function fuyuanBoxDlg.getInstanceAndShow()
	if not _instance then
		_instance = fuyuanBoxDlg:new()
		_instance:OnCreate()
	else
		_instance:SetVisible(true)
	end
	return _instance
end

function fuyuanBoxDlg.getInstanceNotCreate()
	return _instance
end

function fuyuanBoxDlg.DestroyDialog()
	if _instance then 
		if not _instance.m_bCloseIsHide then
            gGetRoleItemManager():RemoveLuaItemNumChangeNotify(_instance.eventItemNumChange)
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
        require("logic.tips.commontipdlg").DestroyDialog()
	end
end

function fuyuanBoxDlg.ToggleOpenClose()
	if not _instance then
		_instance = fuyuanBoxDlg:new()
		_instance:OnCreate()
	else
		if _instance:IsVisible() then
			_instance:SetVisible(false)
		else
			_instance:SetVisible(true)
		end
	end
end

function fuyuanBoxDlg.GetLayoutFileName()-------福缘宝箱
	return "fuyuanbaoxiang.layout"
end

function fuyuanBoxDlg:new()
	local self = {}
	self = Dialog:new()
	setmetatable(self, fuyuanBoxDlg)
	return self
end

function fuyuanBoxDlg:OnCreate()
	Dialog.OnCreate(self)
	local winMgr = CEGUI.WindowManager:getSingleton()

    
	self.cbTipBtn = CEGUI.toPushButton(winMgr:getWindow("fuyuanbaoxiang/cbTipBtn"))---金箱子几率
	self.cbTipBtn:subscribeEvent("Clicked", fuyuanBoxDlg.HandleBtnIntro2Click, self)
	
	self.cbTipBtn2 = CEGUI.toPushButton(winMgr:getWindow("fuyuanbaoxiang/cbTipBtn2"))---银箱子几率
	self.cbTipBtn2:subscribeEvent("Clicked", fuyuanBoxDlg.HandleBtnIntro2Click2, self)
	
	self.cbTipBtn3 = CEGUI.toPushButton(winMgr:getWindow("fuyuanbaoxiang/cbTipBtn3"))---祈福说明
	self.cbTipBtn3:subscribeEvent("Clicked", fuyuanBoxDlg.HandleBtnIntro2Click3, self)
	
	self.cbTipBtn3 = CEGUI.toPushButton(winMgr:getWindow("fuyuanbaoxiang/fuyuan_uit"))
	self.cbTipBtn3:subscribeEvent("Clicked", fuyuanBoxDlg.HandlerBtnSchoolClicked, self)

    
	self.m_Panel = winMgr:getWindow("fuyuanbaoxiang/wupin")
	self.m_Panel2 = winMgr:getWindow("fuyuanbaoxiang/wupin2")
    --???
    self.m_keyItem = CEGUI.Window.toItemCell(winMgr:getWindow("fuyuanbaoxiang/daoju"))
    self.m_keyItem:subscribeEvent("MouseClick",fuyuanBoxDlg.HandleItemClicked,self)
    self.m_keyText = CEGUI.Window.toItemCell(winMgr:getWindow("fuyuanbaoxiang/mingzi"))
    self.m_keyNum = winMgr:getWindow("fuyuanbaoxiang/number")
    --???
    self.m_startBtn = CEGUI.Window.toPushButton(winMgr:getWindow("fuyuanbaoxiang/btn1"))
    self.m_startBtn:subscribeEvent("MouseClick",fuyuanBoxDlg.HandleStartClicked,self)
    self.m_startBtn:setVisible(true)
	self.m_startBtn1 = CEGUI.Window.toPushButton(winMgr:getWindow("fuyuanbaoxiang/btn11"))
    self.m_startBtn1:subscribeEvent("MouseClick",fuyuanBoxDlg.HandleShiLianClicked,self)
    self.m_startBtn1:setVisible(true)
    self.m_startBtn2 = CEGUI.Window.toPushButton(winMgr:getWindow("fuyuanbaoxiang/btn14"))
    self.m_startBtn2:subscribeEvent("MouseClick",fuyuanBoxDlg.HandleStartClicked,self)
    self.m_startBtn2:setVisible(true)
    --??? 
    self.m_itemID = 0
    self.m_boxItem = {}
    self.m_boxBright = {}
	self.m_CellParent = {}
    self.m_itemsData = {}
	self.m_ItemCellEffect = nil
	self.m_NiHongDengEffect = nil
    for i = 1, 25 do---原来for i = 1, 40 do
		self.m_CellParent[i] = winMgr:getWindow("fuyuanbaoxiang/"..i)
        self.m_boxItem[i] = CEGUI.Window.toItemCell(winMgr:getWindow("fuyuanbaoxiang/"..i.."/item"..i))
        self.m_boxItem[i]:subscribeEvent("MouseClick",fuyuanBoxDlg.HandleItemClicked,self)
        self.m_boxBright[i] = CEGUI.Window.toItemCell(winMgr:getWindow("fuyuanbaoxiang/"..i.."/liang"..i))
    end
    
    self.eventItemNumChange = gGetRoleItemManager():InsertLuaItemNumChangeNotify(fuyuanBoxDlg.onEventBagItemNumChange)
	self.m_CloseBtn = winMgr:getWindow("fuyuanbaoxiang/close")
    self.m_CloseBtn:subscribeEvent("Clicked", fuyuanBoxDlg.HandleEndClicked, self)
    --???id  2??????????3????С??
    self.m_KeyIndex = 2
    self.m_Stauts = 0 -- 0??????1???????????2??????????

	self.m_Tips_1 = winMgr:getWindow("fuyuanbaoxiang/yinwenzi")
	self.m_Tips_1:setVisible(false)
	self.m_Tips_2 = winMgr:getWindow("fuyuanbaoxiang/jinwenzi")
	self.m_Tips_2:setVisible(false)
	self.m_Tips_3 = winMgr:getWindow("fuyuanbaoxiang/huanwenzi")
	self.m_Tips_3:setVisible(false)


end
function fuyuanBoxDlg:HandlerBtnSchoolClicked(args)---傲来
fuyuanBoxDlg.DestroyDialog();
	
	if GetTeamManager() and  not GetTeamManager():CanIMove() then
	
		if GetChatManager() then
			GetChatManager():AddTipsMsg(150030)	--??????????
		end
		return true
	end
	local  mapRecord = BeanConfigManager.getInstance():GetTableByName("map.cworldmapconfig"):getRecorder(1605)
	
	if mapRecord == nil then	
		return true;
	end

	if mapRecord.maptype == 1 or mapRecord.maptype == 2  then
	
		local randX = mapRecord.bottomx - mapRecord.topx
		randX = mapRecord.topx + math.random(0, randX)

		local randY = mapRecord.bottomy - mapRecord.topy
		randY = mapRecord.topy + math.random(0, randY)
		--gGetNetConnection():send(fire.pb.mission.CReqGoto(1615, randX, randY));
		
		gGetNetConnection():send(fire.pb.mission.CReqGoto(1605, 51, 39));
        if gGetScene()  then
			gGetScene():EnableJumpMapForAutoBattle(false);
		end
		CharacterLabel.DestroyDialog()
	else
		return false
	end	
		
	return true;
	
end

function fuyuanBoxDlg:HandleBtnIntro2Click()--金箱子说明
	print("fuyuanBoxDlg:HandleInfoClick()")
	local title = MHSD_UTILS.get_resstring(11827)
	local strAllString = MHSD_UTILS.get_resstring(11828)
	local tips1 = require "logic.workshop.tips1"
    tips1.getInstanceAndShow(strAllString, title)
end
function fuyuanBoxDlg:HandleBtnIntro2Click2()--银箱子说明
	print("fuyuanBoxDlg:HandleInfoClick()")
	local title = MHSD_UTILS.get_resstring(11829)
	local strAllString = MHSD_UTILS.get_resstring(11830)
	local tips1 = require "logic.workshop.tips1"
    tips1.getInstanceAndShow(strAllString, title)
end
function fuyuanBoxDlg:HandleBtnIntro2Click3()--祈福说明
	print("fuyuanBoxDlg:HandleInfoClick()")
	local title = MHSD_UTILS.get_resstring(11831)
	local strAllString = MHSD_UTILS.get_resstring(11832)
	local tips1 = require "logic.workshop.tips1"
    tips1.getInstanceAndShow(strAllString, title)
end

function fuyuanBoxDlg:realAwardListRandomSort( num )
	local N = num
	for i = 1, N do
		local j = math.random(N-i+1)+i-1
		self.m_itemsData[i], self.m_itemsData[j] = self.m_itemsData[j], self.m_itemsData[i]
	end
end
function fuyuanBoxDlg.onEventBagItemNumChange(bagid, itemkey, itembaseid) 
    local dlg = require"logic.fuyuanbox.fuyuanboxdlg".getInstanceNotCreate()
    if dlg then
        if itembaseid == dlg.m_itemID then
            dlg:InitKey()
        end
    end
end
function fuyuanBoxDlg:totalTimeNum()
	self.m_totalTime = self.m_baseSpeed * self.m_totalMoveNum + self.m_addSpeed * self:addTime(self.m_addSpeedNum) + self.m_slowSpeed * self:addTime(self.m_slowNum)
end
function fuyuanBoxDlg:addTime( num )
	local totalNum = 0
	if num <= 0 then
		return
	end
	for i = 1, num do
		totalNum = totalNum + i 
	end
	return totalNum
end
function fuyuanBoxDlg:InitDlg(index, npckey)
    self.m_KeyIndex = index
    self.m_NpcKey = npckey
    --????????
    self:InitItem()

    --???????? ??? ???????
    if self.m_KeyIndex == 2 then
        self.m_itemID = tonumber(GameTable.common.GetCCommonTableInstance():getRecorder(284).value)--337943	氪金钥匙
		self.m_startBtn2:setVisible(false)
    --???С?? ??? ?????????
    elseif self.m_KeyIndex == 3 then
        self.m_itemID = tonumber(GameTable.common.GetCCommonTableInstance():getRecorder(285).value)--337942	瑟银钥匙
		self.m_startBtn2:setVisible(false)
	elseif self.m_KeyIndex == 4 then
        self.m_itemID = tonumber(GameTable.common.GetCCommonTableInstance():getRecorder(488).value)--339127	一星幻境钥匙
		self.m_startBtn:setVisible(false)
		self.m_startBtn1:setVisible(false)
	elseif self.m_KeyIndex == 5 then
        self.m_itemID = tonumber(GameTable.common.GetCCommonTableInstance():getRecorder(490).value)--339128	二幻境钥匙
		self.m_startBtn:setVisible(false)
		self.m_startBtn1:setVisible(false)
	elseif self.m_KeyIndex == 6 then
        self.m_itemID = tonumber(GameTable.common.GetCCommonTableInstance():getRecorder(492).value)--339129	三幻境钥匙
		self.m_startBtn:setVisible(false)
		self.m_startBtn1:setVisible(false)
    end
    
    --?????????
    self:InitKey()

    --????????????
    self:InitData()

	if self.m_KeyIndex == 2 then
		self.m_Panel:setVisible(true)--设置图像可见--
	    self.m_Tips_2:setText(MHSD_UTILS.get_resstring(11696))--必得神兜兜
		self.m_Tips_2:setVisible(true)
		self.cbTipBtn2:setVisible(false)
	elseif self.m_KeyIndex == 3 then
		self.m_Tips_1:setText(MHSD_UTILS.get_resstring(11697))--必得5个花豆
		self.m_Tips_1:setVisible(true)
		self.m_Panel2:setVisible(true)--设置图像可见--
		self.cbTipBtn:setVisible(false)--设置图像可见--
		
	elseif self.m_KeyIndex == 4 then
		self.m_Tips_3:setText(MHSD_UTILS.get_resstring(11698))--一星宝箱必得金柳露
		self.m_Tips_3:setVisible(true)
	elseif self.m_KeyIndex == 5 then
		self.m_Tips_3:setText(MHSD_UTILS.get_resstring(11699))--二星宝箱必得金柳露
		self.m_Tips_3:setVisible(true)
	elseif self.m_KeyIndex == 6 then
		self.m_Tips_3:setText(MHSD_UTILS.get_resstring(11700))--三星宝箱内丹宝盒
		self.m_Tips_3:setVisible(true)
	end
    
end
function fuyuanBoxDlg:InitData()
    self.m_addSpeedNum = tonumber(GameTable.common.GetCCommonTableInstance():getRecorder(275).value) -- 5	开奖界面加速格子数量	
    self.m_normalSpeed = tonumber(GameTable.common.GetCCommonTableInstance():getRecorder(276).value) -- 0.3	开奖界面格子正常速度，多少秒1个格子			
	self.m_minSlowNum = tonumber(GameTable.common.GetCCommonTableInstance():getRecorder(277).value)  -- 6	格子移动减速距离结束格子最小数		
	self.m_maxSlowNum = tonumber(GameTable.common.GetCCommonTableInstance():getRecorder(278).value)  -- 10	格子移动减速距离结束格子最大数		
	self.m_minTotalMoveNum = tonumber(GameTable.common.GetCCommonTableInstance():getRecorder(279).value) -- 50	总格子移动数量最小数	
	self.m_maxTotalMoveNum = tonumber(GameTable.common.GetCCommonTableInstance():getRecorder(280).value) -- 60	总格子移动数量最大数		
    self.m_closeTime = tonumber(GameTable.common.GetCCommonTableInstance():getRecorder(281).value) -- 3	自动关闭开奖界面时间	
    self.m_closeTime = self.m_closeTime * 1000

    self.m_curAwardIndex = math.random(1, 25)---原来self.m_curAwardIndex = math.random(1, 40)
    self.m_startIndex = self.m_curAwardIndex

    self.m_realAwardNum = 25 --self.m_realAwardNum = 40
    self.m_elapse = 0     
	self.m_moveNum = 1       -- ??????????????????
	self.m_tTime = 0      
	self.m_speed = 0         -- ???????
	self.m_addSpeed = 120    -- ????????
	self.m_slowSpeed = 120   -- ???????
	self.m_laseTime = 0 -- ????????????????????????????и????
	self.m_slowNum = math.random(self.m_minSlowNum, self.m_maxSlowNum)                -- ????????????
	self.m_totalMoveNum = math.random(self.m_minTotalMoveNum, self.m_maxTotalMoveNum) -- ?????????????????
	self.m_baseSpeed = 1 * self.m_normalSpeed -- ???????
	self.m_totalTime = 0   --  ??????????????
	self.m_closeTime = 0
	self:totalTimeNum()
   
    self:realAwardListRandomSort(25) -- 原来self:realAwardListRandomSort(40)
    --self:changeRealAwardPos(awardId) 
    for i = 1, 25 do--原来for i = 1, 40 do
        local iconManager = gGetIconManager()
        local itemAttrCfg = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(self.m_itemsData[i])
        if itemAttrCfg then
        	self.m_boxItem[i]:SetImage(iconManager:GetItemIconByID(itemAttrCfg.icon) )
	        self.m_boxItem[i]:setID(self.m_itemsData[i])
            ShowItemTreasureIfNeed(self.m_boxItem[i],self.m_itemsData[i])
            SetItemCellBoundColorByQulityItem(self.m_boxItem[i], itemAttrCfg.nquality)
        end

    end

	self.m_ItemCellEffect = gGetGameUIManager():AddUIEffect(self.m_boxItem[self.m_curAwardIndex], MHSD_UTILS.get_effectpath(11077))
    self.m_boxItem[self.m_curAwardIndex]:moveToFront()
    --self.m_boxBright[self.m_curAwardIndex]:setVisible(true)

--	local winMgr = CEGUI.WindowManager:getSingleton()
--	local tmp = winMgr:getWindow("fuyuanbaoxiang")

--	local pos = self.m_CellParent[self.m_curAwardIndex]:getPosition()
--	local x = pos.x.offset + self.m_CellParent[self.m_curAwardIndex]:getPixelSize().width/2
--	local y = pos.y.offset + self.m_CellParent[self.m_curAwardIndex]:getPixelSize().height/2
--	local offx = tmp:getXPosition():asAbsolute(tmp:getParent():getPixelSize().width)--tmp:getPosition().x.offset
--	local offy = tmp:getYPosition():asAbsolute(tmp:getParent():getPixelSize().height)
--	self.m_ItemCellEffect:SetLocation(Nuclear.NuclearPoint(offx + x, offy + y))
    --gGetGameUIManager():AddUIEffect(self.m_boxItem[self.m_curAwardIndex], MHSD_UTILS.get_effectpath(11077))
end
function fuyuanBoxDlg:changeRealAwardPos( awardId )--更改真实奖励位置
	local tmp
	for i = 1, self.m_realAwardNum do
		if self.m_itemsData[i] == awardId then
			tmp = i
		end
	end
	local realIndex = math.floor((self.m_totalMoveNum + self.m_startIndex - 1) % 25)--原来local realIndex = math.floor((self.m_totalMoveNum + self.m_startIndex - 1) % 40)
	if realIndex == 0 then
		realIndex = 12 --原来realIndex = 12
	end
    if tmp < realIndex then
        for i = 1, (realIndex - tmp) do
            self.m_totalMoveNum = self.m_totalMoveNum - 1
        end
    else 
        for i = 1, (realIndex - tmp + self.m_realAwardNum) do
            self.m_totalMoveNum = self.m_totalMoveNum - 1
        end
    end
    self:totalTimeNum()

end
function fuyuanBoxDlg:InitKey()
    local itemAttrCfg = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(self.m_itemID)
    if itemAttrCfg then
        local iconManager = gGetIconManager()
        self.m_keyItem:SetImage(iconManager:GetItemIconByID(itemAttrCfg.icon) )
	    self.m_keyItem:setID(self.m_itemID)
        ShowItemTreasureIfNeed(self.m_keyItem,self.m_itemID)
        SetItemCellBoundColorByQulityItem(self.m_keyItem, itemAttrCfg.nquality)
        local roleItemManager = require("logic.item.roleitemmanager").getInstance()
        local nItemNum = roleItemManager:GetItemNumByBaseID(self.m_itemID)
        if nItemNum == 0 then
        	local huoliColor = "FF06CC11"--没钥匙--红色--FFFF0000
			local textColor = "tl:"..huoliColor.." tr:"..huoliColor.." bl:"..huoliColor.." br:"..huoliColor
			self.m_keyNum:setProperty("TextColours", textColor)
		else	
        	local huoliColor = "FF06CC11"--有钥匙--绿色
			local textColor = "tl:"..huoliColor.." tr:"..huoliColor.." bl:"..huoliColor.." br:"..huoliColor
			self.m_keyNum:setProperty("TextColours", textColor)
		end
        self.m_keyText:setText(itemAttrCfg.name)---钥匙名字
		local colour = "FFD97A23"--钥匙名字--褐色
        if string.len(itemAttrCfg.colour) > 0 then
            local textColor = "tl:"..itemAttrCfg.colour.." tr:"..itemAttrCfg.colour.." bl:"..itemAttrCfg.colour.." br:"..itemAttrCfg.colour
            --self.m_keyText:setProperty("TextColours", textColor)
            self.m_keyText:setProperty("TextColours", colour)--褐色
        end
        --self.m_keyNum:setText(nItemNum.."/1")--钥匙数量1/1
        self.m_keyNum:setText(nItemNum.."")--钥匙数量1/1
    end
end
function fuyuanBoxDlg.Sfunyuan_pro(index)
    local dlg = require"logic.fuyuanbox.fuyuanboxdlg".getInstanceNotCreate()
    if dlg then
        dlg:StartRotate(index)
    end
end
function fuyuanBoxDlg:StartRotate(index)
    local record = BeanConfigManager.getInstance():GetTableByName("game.cschoolwheel"):getRecorder(self.m_KeyIndex)
    if record == nil then
        return
    end
    local strs = StringBuilder.Split(record.items[index],";")
    self.m_Stauts = 1
    self.m_startBtn:setVisible(false)
	self.m_startBtn1:setVisible(false)
    self:changeRealAwardPos(tonumber(strs[1]))
    --gGetGameUIManager():RemoveUIEffect(self.m_boxItem[self.m_curAwardIndex])
end
function fuyuanBoxDlg:InitItem()
    local record = BeanConfigManager.getInstance():GetTableByName("game.cschoolwheel"):getRecorder(self.m_KeyIndex)
    if record == nil then
        return
    end
    for i = 1, 25 do-- for i = 1, 40 do---显示道具数量
        local strs = StringBuilder.Split(record.items[i-1],";")
        local itemAttrCfg = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(tonumber(strs[1]))
        if itemAttrCfg then
            self.m_itemsData[i] = tonumber(strs[1])
        end

    end
end
function fuyuanBoxDlg:HandleItemClicked(args)
	local e = CEGUI.toMouseEventArgs(args)
	local touchPos = e.position
	local nPosX = touchPos.x
	local nPosY = touchPos.y
	
	local index = e.window:getID()
	
	local Commontipdlg = require "logic.tips.commontipdlg"
	local commontipdlg = Commontipdlg.getInstanceAndShow()
	local nType = Commontipdlg.eNormal
	local nItemId = index
	
	commontipdlg:RefreshItem(nType,nItemId,nPosX,nPosY)
end
function fuyuanBoxDlg:HandleEndClicked(args)
	local pro = require "protodef.fire.pb.game.cendxueyuewheel".Create()
    LuaProtocolManager.getInstance():send(pro)

	gGetGameUIManager():RemoveUIEffect(self.m_ItemCellEffect)

    self.DestroyDialog()
	self:GotoNextGoods()
end
function fuyuanBoxDlg:HandleStartClicked(args)
    local roleItemManager = require("logic.item.roleitemmanager").getInstance()
    local nItemNum = roleItemManager:GetItemNumByBaseID(self.m_itemID)

    if nItemNum == 0 then
        local recordQuickBuy = BeanConfigManager.getInstance():GetTableByName(CheckTableName("shop.cquickbuy")):getRecorder(self.m_itemID)
        if not recordQuickBuy then
		    local configItem = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(self.m_itemID)
            if configItem then
		        local name = configItem.name;
		
		        local strbuilder = StringBuilder:new()
		        strbuilder:Set("parameter1", name)
		        local tempStrTip = strbuilder:GetString(MHSD_UTILS.get_msgtipstring(150020))---$parameter1$数量不足
                strbuilder:delete()
		        GetCTipsManager():AddMessageTip(tempStrTip)
            end
        else
            ShopManager:tryQuickBuy(self.m_itemID)
        end
        return
    end
    --??Э??
    local pro = require "protodef.fire.pb.game.cbeginxueyuewheel".Create()
    pro.boxtype = self.m_KeyIndex
    pro.npckey = self.m_NpcKey
	pro.shilian = 1--这个代表不是10连抽
    LuaProtocolManager.getInstance():send(pro)

	--????????Ч
	local winMgr = CEGUI.WindowManager:getSingleton()
	local tmp = winMgr:getWindow("fuyuanbaoxiang/paomadeng")

	local w = tmp:getPixelSize().width/2
	local h = tmp:getPixelSize().height/2

	--gGetGameUIManager():AddUIEffect(tmp, MHSD_UTILS.get_effectpath(11078), true, w, h)--抽奖七彩彩灯

	m_ClickStart = true
end
function fuyuanBoxDlg:HandleShiLianClicked(args)
    local roleItemManager = require("logic.item.roleitemmanager").getInstance()
    local nItemNum = roleItemManager:GetItemNumByBaseID(self.m_itemID)

    if nItemNum < 10 then
        local recordQuickBuy = BeanConfigManager.getInstance():GetTableByName(CheckTableName("shop.cquickbuy")):getRecorder(self.m_itemID)
        if not recordQuickBuy then
		    local configItem = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(self.m_itemID)
            if configItem then
		        local name = configItem.name;
		
		        local strbuilder = StringBuilder:new()
		        strbuilder:Set("parameter1", name)
		        local tempStrTip = strbuilder:GetString(MHSD_UTILS.get_msgtipstring(150020))
                strbuilder:delete()
		        GetCTipsManager():AddMessageTip(tempStrTip)
            end
        else
            ShopManager:tryQuickBuy(self.m_itemID)
        end
        return
    end
    --??Э??
    self.DestroyDialog();
    local pro = require "protodef.fire.pb.game.cbeginxueyuewheel".Create()
    pro.boxtype = self.m_KeyIndex
    pro.npckey = self.m_NpcKey
	pro.shilian = 2--这个代表是10连抽
    LuaProtocolManager.getInstance():send(pro)
end
function fuyuanBoxDlg:update(delta)
    if self.m_Stauts == 1 then
    	self.m_elapse = self.m_elapse + delta
	    if self.m_elapse < self.m_totalTime then -- С???????
		    self.m_tTime = self.m_elapse
		    if self.m_moveNum < self.m_addSpeedNum+1 then -- ????????????
			    self.m_speed = (self.m_addSpeedNum+1 - self.m_moveNum)*self.m_addSpeed + self.m_baseSpeed
		    elseif self.m_moveNum > self.m_totalMoveNum - self.m_slowNum then -- ???????????
			    self.m_speed = (self.m_moveNum - (self.m_totalMoveNum - self.m_slowNum))*self.m_slowSpeed + self.m_baseSpeed
		    else
			    self.m_speed = self.m_baseSpeed   -- ?м?????????
		    end
		    self.m_tTime = self.m_elapse - self.m_laseTime
		    if self.m_tTime > self.m_speed then  
			    if self.m_boxBright[self.m_curAwardIndex] then
				    --self.m_boxBright[self.m_curAwardIndex]:setVisible(false) --??????????????????????
			    end
			    self.m_curAwardIndex = self.m_curAwardIndex + 1
			    self.m_moveNum = self.m_moveNum + 1
			    if self.m_curAwardIndex > self.m_realAwardNum then  
				    self.m_curAwardIndex = 1   -- ?????????
			    end
			    if self.m_boxBright[self.m_curAwardIndex] then
				    --self.m_boxBright[self.m_curAwardIndex]:setVisible(true)

					local winMgr = CEGUI.WindowManager:getSingleton()
					--local tmp = winMgr:getWindow("fuyuanbaoxiang/fuyuandi")--跑马灯读取框--+33--+33
					local tmp = winMgr:getWindow("fuyuanbaoxiang")--跑马灯读取框
	
					local pos = self.m_CellParent[self.m_curAwardIndex]:getPosition()
					local x = pos.x.offset + self.m_CellParent[self.m_curAwardIndex]:getPixelSize().width+445--坐标宽度
					local y = pos.y.offset + self.m_CellParent[self.m_curAwardIndex]:getPixelSize().height+33--坐标高度
					local offx = tmp:getXPosition():asAbsolute(tmp:getParent():getPixelSize().width)--tmp:getPosition().x.offset
					local offy = tmp:getYPosition():asAbsolute(tmp:getParent():getPixelSize().height)
					self.m_ItemCellEffect:SetLocation(Nuclear.NuclearPoint(offx + x, offy + y))
					
					
			    end
			    self.m_laseTime = self.m_laseTime + self.m_speed
			
			    if self.m_moveNum == self.m_totalMoveNum then  -- ???ok???????
				    local pro = require "protodef.fire.pb.game.cendxueyuewheel".Create()
                    LuaProtocolManager.getInstance():send(pro)

						local winMgr = CEGUI.WindowManager:getSingleton()
						local tmp = winMgr:getWindow("fuyuanbaoxiang/paomadeng")
						gGetGameUIManager():RemoveUIEffect(tmp)
			    end
		    end
	    elseif self.m_elapse > self.m_totalTime + self.m_closeTime then

			gGetGameUIManager():RemoveUIEffect(self.m_ItemCellEffect)

		    self.DestroyDialog()
			self:GotoNextGoods()
	    end
    end
end

function fuyuanBoxDlg:GotoNextGoods()
	if not m_ClickStart then
		return
	end

	m_ClickStart = false

	local roleItemManager = require("logic.item.roleitemmanager").getInstance()
	local nItemNum = roleItemManager:GetItemNumByBaseID(self.m_itemID)

	local yinbox = tonumber(GameTable.common.GetCCommonTableInstance():getRecorder(283).value)--183707	秘银宝箱id
	local jinbox = tonumber(GameTable.common.GetCCommonTableInstance():getRecorder(282).value)
	local huanbox = tonumber(GameTable.common.GetCCommonTableInstance():getRecorder(487).value)--183706	赤金宝箱id
	local erbox = tonumber(GameTable.common.GetCCommonTableInstance():getRecorder(489).value)--183709	二幻境宝箱id
	local sanbox = tonumber(GameTable.common.GetCCommonTableInstance():getRecorder(491).value)--183710	三幻境宝箱id

	for i = 1, gGetScene():GetSceneNPCNum() do
		local npc = gGetScene():GetSceneNPC(i)
		if npc:GetNpcTypeID() == 29 and nItemNum >= 1 then
			if self.m_KeyIndex == 2 then
				if npc:GetNpcBaseID() == jinbox then
					GetMainCharacter():OnVisitNpc(npc)
					break
				end
			end
			if self.m_KeyIndex == 3 then
			    if npc:GetNpcBaseID() == yinbox then
					GetMainCharacter():OnVisitNpc(npc)
					break
			    end
			end
			if self.m_KeyIndex == 4 then
			    if npc:GetNpcBaseID() == huanbox then
					GetMainCharacter():OnVisitNpc(npc)
					break
				end
			end
			if self.m_KeyIndex == 5 then
			    if npc:GetNpcBaseID() == erbox then
					GetMainCharacter():OnVisitNpc(npc)
					break
				end
			end
			if self.m_KeyIndex == 6 then
			    if npc:GetNpcBaseID() == sanbox then
					GetMainCharacter():OnVisitNpc(npc)
					break
				end
			end
			
		end
	end
end



return fuyuanBoxDlg