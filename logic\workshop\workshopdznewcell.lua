require "utils.mhsdutils"
require "logic.dialog"
--require "logic.workshop.workshopdzlevelbtn"


Workshopdznewcell = {}
setmetatable(Workshopdznewcell, Dialog)
Workshopdznewcell.__index = Workshopdznewcell

local _instance;

--local nLevelArea = 15

function Workshopdznewcell:new()
    local self = {}
    self = Dialog:new()
	--self.dzDlg = nil
    setmetatable(self, Workshopdznewcell)
    return self
end

function Workshopdznewcell:clearList()
	if not self.cells or #self.cells == 0 then return end
	for _,v in pairs(self.cells) do
		self.container:removeChildWindow(v)
	end
end

function Workshopdznewcell:HandleCellClicked(args)
	local eventargs = CEGUI.toWindowEventArgs(args)
	local nId = eventargs.window:getID()
	--local dzDlg = require "logic.workshop.workshopdznew"
	local dzDlg = require "logic.workshop.workshopdznew".getInstanceOrNot()
	if dzDlg then 
		dzDlg:RefreshLevelArea(nId)
	end
	--[[
	if self.dzDlg then
		self.dzDlg:RefreshLevelArea(nId)
		self.dzDlg = nil
	end
	--]]
	
	--self.DestroyDialog()
	
	self:SetVisible(false)
end

function Workshopdznewcell:OnCreate()
    Dialog.OnCreate(self)
	local winMgr = CEGUI.WindowManager:getSingleton()
    self.container = CEGUI.Window.toScrollablePane(winMgr:getWindow("workshopdznewcell/container"))
	self:clearList()
	self.cells = {}
	local winMgr = CEGUI.WindowManager:getSingleton()
	local sizeScroll = self.container:getPixelSize()
	local nCellH = 60
	local strLevelzi = MHSD_UTILS.get_resstring(351)
    local strLevelArea =  GameTable.common.GetCCommonTableInstance():getRecorder(208).value
    local nLevelArea =  tonumber(strLevelArea) 
	for nIndex=7,nLevelArea do
		local nArea = nIndex*10
		local wnd = winMgr:createWindow("TaharezLook/common_roll") --common_roll --common_equipbj
		wnd:setSize(CEGUI.UVector2(CEGUI.UDim(0, sizeScroll.width), CEGUI.UDim(0, nCellH)))
		wnd:setPosition(CEGUI.UVector2(CEGUI.UDim(0,0),CEGUI.UDim(0,(nIndex-7)*(nCellH+5))))
		--wnd:setProperty("NormalImage", "set:login_1 image:login_text_bg")
		--wnd:setProperty("PushedImage", "set:login_1 image:login_text_bg")
		local nAreba = ""
		if (nArea==70) then
           nAreba = MHSD_UTILS.get_resstring(11833) 
		elseif(nArea==80) then
           nAreba = MHSD_UTILS.get_resstring(11805)     
		elseif(nArea==90) then
           nAreba = MHSD_UTILS.get_resstring(11806)   
		elseif(nArea==100) then
           nAreba = MHSD_UTILS.get_resstring(11807)  
		elseif(nArea==110) then
           nAreba = MHSD_UTILS.get_resstring(11808)  
	    elseif(nArea==120) then
           nAreba = MHSD_UTILS.get_resstring(11809)   
		elseif(nArea==130) then
           nAreba = MHSD_UTILS.get_resstring(11810)    
		elseif(nArea==140) then
           nAreba = MHSD_UTILS.get_resstring(11811)     
		elseif(nArea==150) then
           nAreba = MHSD_UTILS.get_resstring(11812) 
		elseif(nArea==160) then
           nAreba = MHSD_UTILS.get_resstring(11813)   
		elseif(nArea==170) then
           nAreba = MHSD_UTILS.get_resstring(11814)  
        elseif(nArea==180) then
           nAreba = MHSD_UTILS.get_resstring(11815)  
		elseif(nArea==190) then
           nAreba = MHSD_UTILS.get_resstring(11816)  
		elseif(nArea==200) then
           nAreba = MHSD_UTILS.get_resstring(11817)    
		elseif(nArea==210) then
           nAreba = MHSD_UTILS.get_resstring(11818)
		elseif(nArea==220) then
           nAreba = MHSD_UTILS.get_resstring(11819)
		elseif(nArea==230) then
           nAreba = MHSD_UTILS.get_resstring(11820)
		elseif(nArea==240) then
           nAreba = MHSD_UTILS.get_resstring(11821)
		elseif(nArea==250) then
           nAreba = MHSD_UTILS.get_resstring(11822)
		elseif(nArea==260) then
           nAreba = MHSD_UTILS.get_resstring(11823)
		elseif(nArea==270) then
           nAreba = MHSD_UTILS.get_resstring(11824)
		elseif(nArea==280) then
           nAreba = MHSD_UTILS.get_resstring(11825)
		elseif(nArea==290) then
           nAreba = MHSD_UTILS.get_resstring(11826)
		elseif(nArea==300) then
           nAreba = MHSD_UTILS.get_resstring(11827)
		elseif(nArea==310) then
           nAreba = MHSD_UTILS.get_resstring(11828)
		elseif(nArea==320) then
           nAreba = MHSD_UTILS.get_resstring(11829)
		elseif(nArea==330) then
           nAreba = MHSD_UTILS.get_resstring(11830)
		elseif(nArea==340) then
           nAreba = MHSD_UTILS.get_resstring(11831)
		elseif(nArea==350) then
           nAreba = MHSD_UTILS.get_resstring(11832)
		elseif(nArea==360) then
           nAreba = MHSD_UTILS.get_resstring(11833)
		elseif(nArea==370) then
           nAreba = MHSD_UTILS.get_resstring(11834)
		elseif(nArea==380) then
           nAreba = MHSD_UTILS.get_resstring(11835)
		elseif(nArea==390) then
           nAreba = MHSD_UTILS.get_resstring(11836)
		elseif(nArea==400) then
           nAreba = MHSD_UTILS.get_resstring(11837)
		elseif(nArea==410) then
           nAreba = MHSD_UTILS.get_resstring(11838)		

		elseif(nArea==420) then
           nAreba = MHSD_UTILS.get_resstring(11839)	
		elseif(nArea==430) then
           nAreba = MHSD_UTILS.get_resstring(11840)	
		elseif(nArea==440) then
           nAreba = MHSD_UTILS.get_resstring(11841)	
		elseif(nArea==450) then
           nAreba = MHSD_UTILS.get_resstring(11842)			   
		  
		end
		wnd:setID(nArea)
		wnd:subscribeEvent("Clicked", Workshopdznewcell.HandleCellClicked, self)
		self.container:addChildWindow(wnd)
		local strShowTitle = nAreba
		wnd:setText(strShowTitle)
		table.insert(self.cells, wnd)
	end
end

--//========================================
function Workshopdznewcell.getInstance()
    if not _instance then
        _instance = Workshopdznewcell:new()
        _instance:OnCreate()
    end
    return _instance
end

function Workshopdznewcell.getInstanceAndShow()
    if not _instance then
        _instance = Workshopdznewcell:new()
        _instance:OnCreate()
	else
		_instance:SetVisible(true)
    end
    return _instance
end

function Workshopdznewcell.getInstanceNotCreate()
    return _instance
end

function Workshopdznewcell.DestroyDialog()
	if _instance then
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
end
function Workshopdznewcell.closeDialog()
	if not _instance then 
		return
	end

--self.BtnSelLevel	
	
	local dzDlg = require "logic.workshop.workshopdznew".getInstanceOrNot()
	if dzDlg then 
		dzDlg:closeForBeside()
	end
	--[[
	if _instance.dzDlg then
		_instance.dzDlg:closeForBeside() 
	end
	--]]
	
	--[[
	_instance:OnClose()
	_instance = nil
	--]]
	
	_instance:SetVisible(false)
	
	
end

function Workshopdznewcell:OnClose()
    _instance:clearList()
	Dialog.OnClose(self)
	_instance = nil
end

function Workshopdznewcell.getInstanceOrNot()
	return _instance
end

function Workshopdznewcell.GetLayoutFileName()
    return "workshopdznewcell.layout"
end

return Workshopdznewcell
