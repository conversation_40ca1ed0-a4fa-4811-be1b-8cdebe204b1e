QiandaosongliDlg = {}
QiandaosongliDlg.__index = QiandaosongliDlg

local _instance

function QiandaosongliDlg.create()
    if not _instance then
		_instance = QiandaosongliDlg:new()
		_instance:OnCreate()
	end
	return _instance
end

function QiandaosongliDlg.getInstance()
    local Jianglinew = require("logic.qiandaosongli.jianglinewdlg")
    local jlDlg = Jianglinew.getInstanceAndShow()
     if not jlDlg then
        return nil
    end 
    local dlg = jlDlg:showSysId(Jianglinew.systemId.everyDaySign)
	return dlg
end

function QiandaosongliDlg.getInstanceAndShow()

    return QiandaosongliDlg.getInstance()
end

function QiandaosongliDlg.getInstanceNotCreate()
	return _instance
end

function QiandaosongliDlg:remove()
    self:clearData()
    _instance = nil
end

function QiandaosongliDlg:clearData()
    if not self.m_cells then
        return
    end
    for index in pairs( self.m_cells ) do
		local cell = self.m_cells[index]
		if cell then
			cell:OnClose()
		end
	end

end


function QiandaosongliDlg.DestroyDialog()
     require("logic.qiandaosongli.jianglinewdlg").DestroyDialog()
end

function QiandaosongliDlg:new()
	local self = {}
	setmetatable(self, QiandaosongliDlg)
    return self
end

function QiandaosongliDlg:OnCreate()
	local winMgr = CEGUI.WindowManager:getSingleton()

	local layoutName = "qiandaosonglimain.layout"
	self.m_pMainFrame = winMgr:loadWindowLayout(layoutName)
	self.m_bg = CEGUI.toFrameWindow(winMgr:getWindow("qiandaosonglimain"))
	self.m_imgTitle = winMgr:getWindow("qiandaosonglimain/title")
	self.m_txtTotalText = winMgr:getWindow("qiandaosonglimain/talk/txt")
	self.m_scrollReward = CEGUI.toScrollablePane(winMgr:getWindow("qiandaosonglimain/down/back"))
	self.m_txtDay = winMgr:getWindow("qiandaosonglimain/leijitian")
	self.m_txtTimes = winMgr:getWindow("qiandaosonglimain/buqiantian")
	
	self.m_month = 0
	self.m_times = 0
	self.m_fillTimes = 0
	self.m_flag = 0
	self.m_nFillTimes = 0
	self.m_dayNums = 0
	
	self.m_cells = {}
	
    
	self:InitCell()
	
	local mgr = LoginRewardManager:getInstance()
	self:SetData(mgr.signinmonth, mgr.signintimes, mgr.signinrewardflag, mgr.signinsuppsignnums, mgr.signinsuppregdays, mgr.cansuppregtimes)
end

function  QiandaosongliDlg:GetWindow()
    return self.m_pMainFrame
end

function QiandaosongliDlg:InitCell()
	local parentWidth = self.m_scrollReward:getPixelSize().width
	for i=1,31 do
		local curCell = QiandaosongliCell.CreateNewDlg(self.m_scrollReward)
		local cellPerRow = math.floor(parentWidth / curCell.m_width)
		local row = math.floor((i-1) / cellPerRow)
		local x = CEGUI.UDim(0, 1 + ((i-1)%cellPerRow)*curCell.m_width)
		local y = CEGUI.UDim(0, 1 + row*curCell.m_height)
		local pos = CEGUI.UVector2(x,y)
		curCell:GetWindow():setPosition(pos)

		curCell:GetWindow():setVisible(false)
		table.insert(self.m_cells, curCell)
	end
	
end

function QiandaosongliDlg:RefreshText()
	local strbuilder = StringBuilder:new()
	strbuilder:Set("parameter1", self.m_times)
	local strTimes = strbuilder:GetString(MHSD_UTILS.get_resstring( 11162))
    strbuilder:delete()
	self.m_txtDay:setText(strTimes)

	local strbuilderB = StringBuilder:new()
	strbuilderB:Set("parameter1", self.m_nFillTimes)
	local strFillTimes = strbuilderB:GetString(MHSD_UTILS.get_resstring( 11163))
    strbuilderB:delete()
	self.m_txtTimes:setText(strFillTimes)
end

function QiandaosongliDlg:SetData(  month, times, flag, fillTimes, days, cansuppregtimes)
	self.m_month = month
	self.m_times = times
	self.m_flag = flag
	self.m_nFillTimes = fillTimes
	self.m_days = days
    self.m_cansuppregtimes = cansuppregtimes
    local nCansuppregtime = self.m_cansuppregtimes
    if self.m_nFillTimes <= self.m_cansuppregtimes then
        nCansuppregtime = self.m_nFillTimes
    end
    local fillDay = 0
	local daysNum = 0
	for i,v in ipairs(self.m_cells) do
		local curCell = v
		local cfg = self:GetRecord(self.m_month, i)

		curCell:SetID( self.m_month*100 + i )
		curCell:SetTimes( self.m_times )
--        if self.m_days then

--            if self.m_days[i - 1] then
--                curCell:SetBuqian(true)
--            else
--                curCell:SetBuqian(false)
--            end

--        end
		if self.m_flag == 0 then
			if self.m_times + 1 == i then
				curCell:SetFlag(self.m_flag)
			end
		elseif self.m_flag == 1 then
			if self.m_times == i then
				curCell:SetFlag(self.m_flag)
			end
		end

        if self.m_flag == 0 then
            if nCansuppregtime > daysNum and i > self.m_times + 1 then
                curCell:SetBuqian(true)
                daysNum = daysNum + 1
            else
                curCell:SetBuqian(false)
            end
        elseif self.m_flag == 1 then
            if nCansuppregtime > daysNum and i > self.m_times then
                curCell:SetBuqian(true)
                daysNum = daysNum + 1
            else
                curCell:SetBuqian(false)
            end

        end

		curCell:RefreshShow()
		
		if cfg == nil and daysNum == 0 then
			daysNum = i
		end
		
		if i == 31 then
			daysNum = 31
		end
	end
	
	self.m_dayNums = daysNum
	
	self:RefreshText()
end

function QiandaosongliDlg:GetRecord( month, day )
	local tb = BeanConfigManager.getInstance():GetTableByName("game.cqiandaojiangli")
	local id = month * 100 + day
	return (tb:getRecorder(id))
end

return QiandaosongliDlg
