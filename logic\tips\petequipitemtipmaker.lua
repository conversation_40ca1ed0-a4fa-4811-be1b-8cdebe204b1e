local Petitemtipmaker = {}
local NORMAL_RED = "ffFF0000"
local NORMAL_OrangeRed = "ffFF7F50"
function Petitemtipmaker.makeTip(commonTip,nItemId,pObj)
    Petitemtipmaker.color_NORMAL_RED = CEGUI.ColourRect(CEGUI.PropertyHelper:stringToColour(NORMAL_RED))
	Petitemtipmaker.color_NORMAL_OrangeRed = CEGUI.ColourRect(CEGUI.PropertyHelper:stringToColour(NORMAL_OrangeRed))
	local pEquipData = pObj
	if pObj then
	 pEquipData=pObj
	end
    local strJichushuxing = require "utils.mhsdutils".get_resstring(122)
    commonTip:AppendText(CEGUI.String(strJichushuxing), Petitemtipmaker.color_NORMAL_OrangeRed)
    commonTip:AppendBreak()
	
	local propertyCfg = BeanConfigManager.getInstance():GetTableByName("item.cattributedesconfig"):getRecorder(pEquipData.petequipprokey)
	if propertyCfg ~=nil then   
	if propertyCfg and propertyCfg.id ~= -1 then
	   local strTitleName = propertyCfg.name
	   local nValue = pEquipData.petequipprovalue
	   if pEquipData.petequipprokey == 1500 then
	     nValue=nValue/20
	   end
	 
        strTitleName = strTitleName .. " " .. "+" .. tostring(nValue)
        strTitleName = "  " .. strTitleName
        strTitleName = CEGUI.String(strTitleName)
        commonTip:AppendText(strTitleName, Petitemtipmaker.color_NORMAL_RED)
	end
	end
	return true

end

return Petitemtipmaker
