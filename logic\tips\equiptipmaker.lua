local Equiptipmaker = {}



--[[
local NORMAL_YELLOW = "fffbdc47"
local NORMAL_BLUE = "ff00d8ff " -- "ff5fbae0"  
local NORMAL_GREEN = "ff17ed15 " --"ff33ff00" 
local NORMAL_WHITE = "fffff2df"
local NORMAL_PURPLE = "ffff35fd"
--]]

local NORMAL_YELLOW = "ffFFD700"  --基础、耐久、评分框架颜色
local NORMAL_BLUE = "ff33ff00" -- "ff5fbae0"   --双加字的颜色
local NORMAL_GREEN = "FF06cc11" --"ff33ff00" 
local NORMAL_WHITE = "fffff2df"
local NORMAL_PURPLE = "ff5fbae0"  --特技颜色
local NORMAL_GOLD = "FFff69b4"
local NORMAL_GRAY = "FF808080"
local NORMAL_PINK = "FFEE82EE" -- "ff5fbae0" 

local GemColor = "ff5fbae0" --"FF009ddb"  --宝石颜色

function Equiptipmaker.makeTip(richBox,nItemId,pObj,nItemIdInBody,pObjInBody)

    Equiptipmaker.color_NORMAL_YELLOW = CEGUI.ColourRect(CEGUI.PropertyHelper:stringToColour(NORMAL_YELLOW))
    Equiptipmaker.color_NORMAL_WHITE = CEGUI.ColourRect(CEGUI.PropertyHelper:stringToColour(NORMAL_WHITE))
    Equiptipmaker.color_NORMAL_GREEN = CEGUI.ColourRect(CEGUI.PropertyHelper:stringToColour(NORMAL_GREEN))
    Equiptipmaker.color_NORMAL_BLUE = CEGUI.ColourRect(CEGUI.PropertyHelper:stringToColour(NORMAL_BLUE))
    Equiptipmaker.color_NORMAL_PURPLE = CEGUI.ColourRect(CEGUI.PropertyHelper:stringToColour(NORMAL_PURPLE))
    Equiptipmaker.color_NORMAL_GOLD = CEGUI.ColourRect(CEGUI.PropertyHelper:stringToColour(NORMAL_GOLD))
    Equiptipmaker.color_NORMAL_PINK = CEGUI.ColourRect(CEGUI.PropertyHelper:stringToColour(NORMAL_PINK))

    Equiptipmaker.color_GemColor = CEGUI.ColourRect(CEGUI.PropertyHelper:stringToColour(GemColor))


    local colorStr = richBox:GetColourString():c_str()
    if colorStr ~= "FFFFFFFF" then
        Equiptipmaker.color_NORMAL_WHITE = CEGUI.ColourRect(CEGUI.PropertyHelper:stringToColour(colorStr))
    end

    LogInfo("Equiptipmaker.maketips(nItemId,pObj)",pObj)
    local itemAttrCfg = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(nItemId)
    if not itemAttrCfg then
        LogInfo("Equiptipmaker:makeTip=error=self.nItemId"..nItemId)
        return false
    end
    local pEquipData = nil
    if pObj then
        pEquipData = pObj
    end

    --local nItemIdInBody = 0
    --local pObjInBody = nil

    Equiptipmaker.makeLine(richBox)
    --基础属性
    Equiptipmaker.makeBaseProperty(richBox,nItemId,pEquipData,nItemIdInBody,pObjInBody)
    --附加属性
    Equiptipmaker.makeAddProperty(richBox,nItemId,pEquipData)
    --装备技能
    Equiptipmaker.makeSkill(richBox,nItemId,pEquipData)
    --打造者
    Equiptipmaker.makeProducter(richBox,nItemId,pEquipData)
    --宝石
    Equiptipmaker.makeGem(richBox,nItemId,pEquipData)
    --打符
    Equiptipmaker.makeFumoProperty(richBox,nItemId,pEquipData)
    --耐久
    Equiptipmaker.makeEndure(richBox,nItemId,pEquipData)
    --评分
    Equiptipmaker.makeScore(richBox,nItemId,pEquipData)
    --适合角色
    Equiptipmaker.makeequipsit(richBox,nItemId,pEquipData)
    Equiptipmaker.makeequipsittips(richBox,nItemId,pEquipData)
    Equiptipmaker.makeSex(richBox,nItemId,pEquipData)
    --适合职业
    Equiptipmaker.makeCareer(richBox,nItemId,pEquipData)
    return true
end


function Equiptipmaker.makeLine(richBox)
    --richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("goods_fengexian"))
    richBox:AppendBreak()
end

function Equiptipmaker.GetMinAndMax(vMin,vMax)
    local nMinNum = vMin:size()
    local nMaxNum = vMax:size()
    local nMin = 0
    local nMax = 0
    if nMinNum>0 then
        nMin = vMin[0]
    end
    if nMaxNum>0 then
        nMax = vMax[nMaxNum-1]
    end
    return nMin,nMax
end

function Equiptipmaker.GetPropertyStrData(nEquipId,vCellData)
    local eauipAttrCfg = BeanConfigManager.getInstance():GetTableByName("item.cequipitemattr"):getRecorder(nEquipId)
    if not eauipAttrCfg then
        return
    end
    local nbaseAttrId = eauipAttrCfg.baseAttrId
    local eauipAddAttrCfg = BeanConfigManager.getInstance():GetTableByName("item.cequipiteminfo"):getRecorder(nbaseAttrId)
    if eauipAddAttrCfg == nil then
        return
    end

    local vAllId = {}
    vAllId[#vAllId+1] = {}
    vAllId[#vAllId].nId = eauipAddAttrCfg.shuxing1id
    vAllId[#vAllId].vMin = eauipAddAttrCfg.shuxing1bodongduanmin
    vAllId[#vAllId].vMax = eauipAddAttrCfg.shuxing1bodongduanmax
    vAllId[#vAllId+1] = {}
    vAllId[#vAllId].nId = eauipAddAttrCfg.shuxing2id
    vAllId[#vAllId].vMin = eauipAddAttrCfg.shuxing2bodongduanmin
    vAllId[#vAllId].vMax = eauipAddAttrCfg.shuxing2bodongduanmax
    vAllId[#vAllId+1] = {}
    vAllId[#vAllId].nId = eauipAddAttrCfg.shuxing3id
    vAllId[#vAllId].vMin = eauipAddAttrCfg.shuxing3bodongduanmin
    vAllId[#vAllId].vMax = eauipAddAttrCfg.shuxing3bodongduanmax
    local strBolangzi = MHSD_UTILS.get_resstring(11001)
    for nIndex=1,#vAllId do
        local objPropertyData = vAllId[nIndex]
        local nPropertyId = objPropertyData.nId
        local nTypeNameId = math.floor(nPropertyId/10)
        nTypeNameId = nTypeNameId *10
        nPropertyId = nTypeNameId
        local propertyCfg =  BeanConfigManager.getInstance():GetTableByName("item.cattributedesconfig"):getRecorder(nPropertyId)
        if propertyCfg and propertyCfg.id ~= -1 then
            local strPropertyName = propertyCfg.name
            if strPropertyName==nil then
                strPropertyName = "empty"
            end
            local nMin,nMax = Equiptipmaker.GetMinAndMax(objPropertyData.vMin,objPropertyData.vMax)
            vCellData[#vCellData +1] = {}
            vCellData[#vCellData].strPropertyName = strPropertyName
            if nMin == nMax then
                vCellData[#vCellData].strAddValue =  "+"..tostring(nMin)
            else
                vCellData[#vCellData].strAddValue =  "+"..tostring(nMin)..strBolangzi..tostring(nMax)

            end
        end
    end

end

--vCellData = {strPropertyName ,strAddValue }
function Equiptipmaker.makeBaseProperty_range(richBox,nItemId)

    local strJichushuxing = require "utils.mhsdutils".get_resstring(122)
    richBox:AppendText(CEGUI.String(strJichushuxing),Equiptipmaker.color_NORMAL_YELLOW)
    richBox:AppendBreak()

    local vCellData = {}
    Equiptipmaker.GetPropertyStrData(nItemId,vCellData)

    for nIndex=1,#vCellData do
        local strTitleName = vCellData[nIndex].strPropertyName
        local strRange = vCellData[nIndex].strAddValue
        strTitleName = "  "..strTitleName.."  "..strRange
        strTitleName = CEGUI.String(strTitleName)

        richBox:AppendText(strTitleName,Equiptipmaker.color_NORMAL_WHITE)
        richBox:AppendBreak()
    end
end
--pObjInBody==nil   baseitemid
function Equiptipmaker.makeBaseProperty(richBox,nItemId,pEquipData,nItemIdInBody,pObjInBody)

    if not pEquipData then
        Equiptipmaker.makeBaseProperty_range(richBox,nItemId)
        return
    end
    --if pEquipData then
    local vcBaseKey = pEquipData:GetBaseEffectAllKey()
    if #vcBaseKey <= 0 then
        return
    end
    local basesum = 0;
    local extrsum = 0;
    for nIndex=1,#vcBaseKey do
        local nBaseId = vcBaseKey[nIndex]
        local nBaseValue = pEquipData:GetBaseEffect(nBaseId)
        local nextraBaseValue = pEquipData:GetExtraBaseEffect(nBaseId)
        if nBaseValue~=0 then
            local propertyCfg =  BeanConfigManager.getInstance():GetTableByName("item.cattributedesconfig"):getRecorder(nBaseId)
            if propertyCfg and propertyCfg.id ~= -1 then
                local strTitleName = propertyCfg.name
                local nValue = math.abs(nBaseValue)
                if nBaseValue > 0 then
                    basesum = basesum + nBaseValue
                elseif nBaseValue < 0 then
                end
                local nextraValue =  math.abs(nextraBaseValue)
                if nextraValue ~= 0 then
                    extrsum = extrsum + nextraValue
                end
            end
        end
    end
    -- local strJichushuxing = require "utils.mhsdutils".get_resstring(1379)
    -- strJichushuxing = "熔炼" ..strJichushuxing
    -- local ronglianrate = extrsum / basesum
    -- local ronglianratestr = string.format("%.0f",ronglianrate * 100).."%"
    -- richBox:AppendText(CEGUI.String(strJichushuxing),Equiptipmaker.color_NORMAL_YELLOW)
    -- richBox:AppendText(CEGUI.String(ronglianratestr),Equiptipmaker.color_NORMAL_YELLOW)
    -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("wuxing"))
    -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("wuxing"))
    -- richBox:AppendBreak()
    -- local strJichushuxing1 = require "utils.mhsdutils".get_resstring(11039)
    -- richBox:AppendBreak()
    -- if 0 < ronglianrate  and  ronglianrate < 0.1 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 1星"
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendBreak()
    -- end
    -- if 0.1 <= ronglianrate  and  ronglianrate < 0.2 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 2星"
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendBreak()
    -- end
    -- if 0.2 <= ronglianrate  and  ronglianrate < 0.3 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 3星"
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendBreak()
    -- end
    -- if 0.3 <= ronglianrate  and  ronglianrate < 0.4 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 4星"
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendBreak()
    -- end
    -- if 0.4 <= ronglianrate  and  ronglianrate < 0.5 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 5星"
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendBreak()
    -- end
    -- if 0.5 <= ronglianrate  and  ronglianrate < 0.6 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 6星"
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendBreak()
    -- end
    -- if 0.6 <= ronglianrate  and  ronglianrate < 0.7 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 7星"
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendBreak()
    -- end
    -- if 0.7 <= ronglianrate  and  ronglianrate < 0.8 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 8星"
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendBreak()
    -- end
    -- if 0.8 <= ronglianrate  and  ronglianrate < 0.9 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 9星"
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendBreak()
    -- end
    -- if 0.9 <= ronglianrate  and  ronglianrate < 1 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 10星"
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendBreak()
    -- end
    -- if 1 <= ronglianrate  and  ronglianrate < 1.1 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 11星 "
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))

        -- richBox:AppendBreak()
    -- end
    -- if 1.1 <= ronglianrate  and  ronglianrate < 1.2 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 12星 "
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendBreak()
    -- end
    -- if 1.2 <= ronglianrate  and  ronglianrate < 1.3 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 13星 "
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendBreak()
    -- end
    -- if 1.3 <= ronglianrate  and  ronglianrate < 1.4 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 14星 "
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendBreak()
    -- end
    -- if 1.4 <= ronglianrate  and  ronglianrate < 1.5 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 15星 "
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendBreak()
    -- end
    -- if 1.5 <= ronglianrate  and  ronglianrate < 1.6 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 16星 "
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendBreak()
    -- end
    -- if 1.6 <= ronglianrate  and  ronglianrate < 1.7 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 17星 "
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendBreak()
    -- end
    -- if 1.7 <= ronglianrate  and  ronglianrate < 1.8 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 18星 "
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendBreak()
    -- end
    -- if 1.8 <= ronglianrate  and  ronglianrate < 1.9 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 19星 "
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendBreak()
    -- end
    -- if 1.9 <= ronglianrate  and  ronglianrate < 2 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 20星 "
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendBreak()
    -- end
    -- if 2 <= ronglianrate  and  ronglianrate < 2.1 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 21星 "
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendBreak()
    -- end
    -- if 2.1 <= ronglianrate  and  ronglianrate < 2.2 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 22星 "
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendBreak()
    -- end
    -- if 2.2 <= ronglianrate  and  ronglianrate < 2.3 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 23星 "
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendBreak()
    -- end
    -- if 2.3 <= ronglianrate  and  ronglianrate < 2.4 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 24星 "
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendBreak()
    -- end
    -- if 2.4 <= ronglianrate  and  ronglianrate < 2.5 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 25星 "
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendBreak()
    -- end
    -- if 2.5 <= ronglianrate  and  ronglianrate < 2.6 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 26星 "
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendBreak()
    -- end
    -- if 2.6 <= ronglianrate  and  ronglianrate < 2.7 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 27星 "
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendBreak()
    -- end
    -- if 2.7 <= ronglianrate  and  ronglianrate < 2.8 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 28星 "
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendBreak()
    -- end
    -- if 2.8 <= ronglianrate  and  ronglianrate < 2.9 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 29星 "
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendBreak()
    -- end
    -- if 2.9 <= ronglianrate  and  ronglianrate < 3 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 30星 "
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendBreak()
    -- end
    -- if 3 <= ronglianrate  and  ronglianrate < 3.9 then
        -- strJichushuxing1 =  "熔炼"..strJichushuxing1.." 最高星 "
        -- richBox:AppendText(CEGUI.String(strJichushuxing1),Equiptipmaker.color_NORMAL_YELLOW)
        -- richBox:AppendBreak()
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("taiyang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("yueliang"))
        -- richBox:AppendImage(CEGUI.String("common_pack"),CEGUI.String("xiaowuxing"))
        -- richBox:AppendBreak()
    -- end

    local strJichushuxing = require "utils.mhsdutils".get_resstring(122)
    richBox:AppendText(CEGUI.String(strJichushuxing),Equiptipmaker.color_NORMAL_YELLOW)
    richBox:AppendBreak()
    local ronglian={}
    for nIndex=1,#vcBaseKey do
        local nBaseId = vcBaseKey[nIndex]
        local nBaseValue = pEquipData:GetBaseEffect(nBaseId)
        local nextraBaseValue = pEquipData:GetExtraBaseEffect(nBaseId)
        if nBaseValue~=0 then
            local propertyCfg =  BeanConfigManager.getInstance():GetTableByName("item.cattributedesconfig"):getRecorder(nBaseId)
            if propertyCfg and propertyCfg.id ~= -1 then
                local strTitleName = propertyCfg.name
                local nValue = math.abs(nBaseValue)
                local nextraValue =  math.abs(nextraBaseValue)
                if nBaseValue > 0 then
                    strTitleName = strTitleName.." ".."+"..tostring(nValue)
                    basesum = basesum + nBaseValue
                elseif nBaseValue < 0 then
                    strTitleName = strTitleName.." ".."-"..tostring(nValue)
                end
                strTitleName = "  "..strTitleName
                strTitleName = CEGUI.String(strTitleName)
                richBox:AppendText(strTitleName,Equiptipmaker.color_NORMAL_WHITE)
                local nextraValue =  math.abs(nextraBaseValue)
                if nextraValue ~= 0 then
                    extrsum = extrsum + nextraValue
                    local str = "  "..propertyCfg.name.."+"..tostring(nextraValue)
                    table.insert(ronglian, str)
                    -- str = CEGUI.String(str)
                    -- richBox:AppendText(str,Equiptipmaker.color_NORMAL_GOLD)
                end
                --=====================================
                if pObjInBody then
                    local nValueInBody = Equiptipmaker.getPropertyValue(nBaseId,pObjInBody)
                    local sum = nBaseValue+nextraValue
                    local nCha = sum - nValueInBody
                    if nCha > 0 then
                        richBox:AppendImage(CEGUI.String("shopui"), CEGUI.String("shop_up"));
                    elseif nCha < 0 then
                        richBox:AppendImage(CEGUI.String("shopui"),CEGUI.String("shop_down"));
                    end
                end
                --=====================================

                richBox:AppendBreak()
            end

        end
    end
if #ronglian>0 then
    local rongliantext = require "utils.mhsdutils".get_resstring(123)
    local strx = CEGUI.String(rongliantext)
    richBox:AppendText(strx,Equiptipmaker.color_NORMAL_YELLOW)
    richBox:AppendBreak()
    for i = 1, #ronglian do
        local xx=ronglian[i]
        xx = CEGUI.String(xx)
        richBox:AppendText(xx,Equiptipmaker.color_NORMAL_WHITE)
        richBox:AppendBreak()
    end
end


    --end
end

function Equiptipmaker.getPropertyValue(nBaseId,pObjInBody)
    local pEquipData = nil
    if pObjInBody then
        pEquipData = pObjInBody
    end

    if pEquipData then
        local vcBaseKey = pEquipData:GetBaseEffectAllKey()
        for nIndex=1,#vcBaseKey do
            local nBaseIdInBody = vcBaseKey[nIndex]
            local nBaseValue = pEquipData:GetBaseEffect(nBaseId)
            local nExtrBaseValue = pEquipData:GetExtraBaseEffect(nBaseId)
            if nBaseId == nBaseIdInBody then
                return nBaseValue+nExtrBaseValue
            end
        end
    end
    return 0
end

function Equiptipmaker.makeAddProperty(richBox,nItemId,pEquipData)

    if not pEquipData then
        return
    end
    local equipEffectTable = GameTable.item.GetCEquipEffectTableInstance():getRecorder(nItemId)----leixingbujian
    if not equipEffectTable then
        print("get equipEffectTable failed")
        return
    end----leixingbujian

    -- if pEquipData then
    local vPlusKey = pEquipData:GetPlusEffectAllKey()
    for nIndex=1,#vPlusKey do
        local nPlusId = vPlusKey[nIndex]
        local mapPlusData = pEquipData:GetPlusEffect(nPlusId)
        if mapPlusData.attrvalue ~= 0 then

            local nPropertyId = mapPlusData.attrid
            local nPropertyValue = mapPlusData.attrvalue
            local propertyCfg =  BeanConfigManager.getInstance():GetTableByName("item.cattributedesconfig"):getRecorder(nPropertyId)
            if propertyCfg and propertyCfg.id ~= -1 then
                local strTitleName = propertyCfg.name
                local nValue = math.abs(nPropertyValue)
                if nPropertyValue > 0 then
                    strTitleName = strTitleName.." ".."+"..tostring(nValue)
                else
                    strTitleName = strTitleName.." ".."-"..tostring(nValue)
                end
                local strEndSpace = "  "
                local strBeginSpace = "  "
                strTitleName = strTitleName..strEndSpace
                strTitleName = strBeginSpace..strTitleName

                strTitleName = CEGUI.String(strTitleName)

                if equipEffectTable.eequiptype > 5 then
                    richBox:AppendText(strTitleName, Equiptipmaker.color_NORMAL_GREEN)
                    if nIndex == 1 then
                        richBox:AppendBreak()
                    end
                    if nIndex == 2 then
                        richBox:AppendBreak()
                    end
                    if nIndex == 3 then
                        richBox:AppendBreak()
                    end
                    if nIndex == 4 then
                        richBox:AppendBreak()
                    end
                else
                    richBox:AppendText(strTitleName, Equiptipmaker.color_NORMAL_BLUE)
                end


            end
        end --if end
    end --for end
    --========================
    if #vPlusKey > 0 then
        richBox:AppendBreak()
    end
    -- end --if end

end

--[[
11286	��ʱ
11287	��Ч����
11288	ʣ��
��ʱ���� + 10  ʣ�� 3�� 
��Ч����
--]]




function Equiptipmaker.makeFumoProperty(richBox,nItemId,pEquipData)
    if not pEquipData then
        return
    end

    local nFumoCount = pEquipData:getFumoCount()
    if nFumoCount <= 0 then
        return
    end

    local strLinshizi = require "utils.mhsdutils".get_resstring(11286)
    local strLeftzi = require "utils.mhsdutils".get_resstring(11288)
    local strEndTozi = require "utils.mhsdutils".get_resstring(11287)

    --richBox:AppendText(CEGUI.String(strJichushuxing),Equiptipmaker.color_NORMAL_YELLOW)
    --richBox:AppendBreak()

    for nIndexCount=0,nFumoCount-1 do
        local vFumoKey = pEquipData:GetFumoIdWithIndex(nIndexCount)

        local bHaveFumoInTime = require("logic.tips.commontiphelper").isHaveFumoInTime(pEquipData,nIndexCount)
        if bHaveFumoInTime then
            for nIndex=0,vFumoKey:size()-1 do
                local nFumoId = vFumoKey[nIndex]
                local nFumoValue = pEquipData:GetFumoValueWithId(nIndexCount,nFumoId)

                local propertyCfg =  BeanConfigManager.getInstance():GetTableByName("item.cattributedesconfig"):getRecorder(nFumoId)
                if propertyCfg and propertyCfg.id ~= -1 then
                    local strTitleName = propertyCfg.name
                    strTitleName = strLinshizi..strTitleName
                    local nValue = math.abs(nFumoValue)
                    if nFumoValue > 0 then
                        strTitleName = strTitleName.." ".."+"..tostring(nValue)
                    elseif nFumoValue < 0 then
                        strTitleName = strTitleName.." ".."-"..tostring(nValue)
                    end
                    strTitleName = CEGUI.String(strTitleName)
                    richBox:AppendText(strTitleName, Equiptipmaker.color_NORMAL_GREEN)
                    --richBox:AppendBreak()
                    --richBox:AppendBreak()
                end
                -------------------------------
                local fumoData = pEquipData:getFumoDataWidthIndex(nIndexCount)
                local nFomoEndTime = 0
                if fumoData then
                    nFomoEndTime = fumoData.nFomoEndTime
                end
                if nFomoEndTime > 0 then
                    local nServerTime = gGetServerTime() /1000
                    local nLeftSecond = nFomoEndTime / 1000 - nServerTime
                    local timeStr =  require("logic.tips.commontiphelper").GetLeftTimeString(nLeftSecond) --Equiptipmaker.GetLeftTimeString(nLeftSecond)
                    strEndTozi = "  "..strLeftzi..timeStr
                    strEndTozi = CEGUI.String(strEndTozi)
                    richBox:AppendText(strEndTozi, Equiptipmaker.color_NORMAL_GREEN)
                    richBox:AppendBreak()
                end
                -------------------------------
            end
        end
    end
end

--1237 %d��%d��%d�� 
--1238 %dʱ%d��%d��
--1239 %d��%d��
--1240 %dСʱ%d����
--1241 %d����
--1242 %dСʱ

function Equiptipmaker.GetLeftTimeString(seconds)
    local strHourzi = require "utils.mhsdutils".get_resstring(1242)
    local strMinutezi = require "utils.mhsdutils".get_resstring(1241)


    local hours = math.floor(seconds/3600)
    local leftMinSec = seconds - hours*3600

    local mins = math.floor(leftMinSec/60)
    local secs = math.floor(leftMinSec - mins*60)

    if hours < 0 then
        hours = 0
    end
    if mins < 0 then
        mins = 0
    end
    if secs < 0 then
        secs = 0
    end

    local strTime = ""
    if hours > 0 then

        ---local sb = StringBuilder:new()
        --sb:Set("parameter1", strTitle)
        --sb:Set("parameter2", tostring(nStep))
        --strMsg = sb:GetString(strMsg)
        --sb:delete()

        --strTime = hours ..strHourzi.. mins..strMinutezi
        strTime = string.format(strHourzi,hours)
        -- strTime = strTime..string.format(strMinutezi,mins)

    else
        strTime = string.format(strMinutezi,mins)
    end
    return strTime
end


function Equiptipmaker.makeGem(richBox,nItemId,pEquipData)

    if not pEquipData then
        return
    end

    local vcGemList = pEquipData:GetGemlist()
    if vcGemList:size() <= 0 then
        return
    end

    local strBaoshishuxing = require "utils.mhsdutils".get_resstring(125)
    strBaoshishuxing = CEGUI.String(strBaoshishuxing)
    richBox:AppendText(strBaoshishuxing,Equiptipmaker.color_NORMAL_YELLOW)
    richBox:AppendBreak();

    for nIndex=0,vcGemList:size()-1 do
        local nGemId = vcGemList[nIndex]
        local gemEffectTable = BeanConfigManager.getInstance():GetTableByName("item.cgemeffect"):getRecorder(nGemId)
        local itemAttrTable = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(nGemId)
        local strTitleName = "  "
        if itemAttrTable then
            strTitleName = strTitleName..itemAttrTable.name
            strTitleName = strTitleName.."  "
        end
        if gemEffectTable then
            strTitleName = strTitleName..gemEffectTable.effectdes
        end

        strTitleName = CEGUI.String(strTitleName)
        richBox:AppendText(strTitleName, Equiptipmaker.color_GemColor)
        richBox:AppendBreak()
    end



end

function Equiptipmaker.makeSkill(richBox,nItemId,pEquipData)

    if not pEquipData then
        return
    end
    local nTejiId = pEquipData.skillid
    local nTexiaoId = pEquipData.skilleffect
    local nNewTejiId = pEquipData.newskillid
    local nNewTexiaoId = pEquipData.newskilleffect

    local texiaoTable = BeanConfigManager.getInstance():GetTableByName("skill.cequipskillinfo"):getRecorder(nTexiaoId)
    if texiaoTable and texiaoTable.id ~= -1 then
        local strTeXiaozi = require "utils.mhsdutils".get_resstring(11003)
        strTeXiaozi = "  "..strTeXiaozi.." "..texiaoTable.name

        strTeXiaozi = CEGUI.String(strTeXiaozi)
        richBox:AppendText(strTeXiaozi, Equiptipmaker.color_NORMAL_PURPLE)
        richBox:AppendBreak();
    end

    local tejiTable = BeanConfigManager.getInstance():GetTableByName("skill.cequipskillinfo"):getRecorder(nTejiId)
    if tejiTable and tejiTable.id ~= -1 then
        local strTejizi = require "utils.mhsdutils".get_resstring(11002)
        strTejizi = "  "..strTejizi.." "..tejiTable.name

        strTejizi = CEGUI.String(strTejizi)
        richBox:AppendText(strTejizi, Equiptipmaker.color_NORMAL_PURPLE)
        richBox:AppendBreak();
    end
    local newtexiaoTable = BeanConfigManager.getInstance():GetTableByName("skill.cequipskillinfo"):getRecorder(nNewTexiaoId)
    if newtexiaoTable and newtexiaoTable.id ~= -1 then
        local strTeXiaozi = require "utils.mhsdutils".get_resstring(12713)
        strTeXiaozi = "  "..strTeXiaozi.." "..newtexiaoTable.name

        strTeXiaozi = CEGUI.String(strTeXiaozi)
        richBox:AppendText(strTeXiaozi, Equiptipmaker.color_NORMAL_PURPLE)
        richBox:AppendBreak();
    end

    local newtejiTable = BeanConfigManager.getInstance():GetTableByName("skill.cequipskillinfo"):getRecorder(nNewTejiId)
    if newtejiTable and newtejiTable.id ~= -1 then
        local strTejizi = require "utils.mhsdutils".get_resstring(12714)
        strTejizi = "  "..strTejizi.." "..newtejiTable.name

        strTejizi = CEGUI.String(strTejizi)
        richBox:AppendText(strTejizi, Equiptipmaker.color_NORMAL_PURPLE)
        richBox:AppendBreak();
    end
    local itemAttrTable = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(nItemId)
    if itemAttrTable.needLevel == 0 or itemAttrTable.needLevel == 1 then
        strTejizi = "  特效 无级别限制"
        strTejizi = CEGUI.String(strTejizi)
        richBox:AppendText(strTejizi, Equiptipmaker.color_NORMAL_PURPLE)
        richBox:AppendBreak();
    end

end
function Equiptipmaker.makeequipsit(richBox,nItemId,pEquipData)
    if not pEquipData then
        return
    end
    local equipsit = pEquipData.equipsit
    if equipsit>0 then
        local strequipsitzi = require "utils.mhsdutils".get_resstring(11691)
        richBox:AppendText(CEGUI.String(strequipsitzi..":"),Equiptipmaker.color_NORMAL_GREEN)
        richBox:AppendBreak()
        local equipsitTable = BeanConfigManager.getInstance():GetTableByName("item.cequipsit"):getRecorder(equipsit)
        if equipsitTable and equipsitTable.id ~= -1 then
            strequipsitzi = equipsitTable.name
            strequipsitzi = CEGUI.String(strequipsitzi)
            richBox:AppendText(strequipsitzi,Equiptipmaker.color_NORMAL_PURPLE)
            richBox:AppendBreak();
        end
    end

end
function Equiptipmaker.makeequipsittips(richBox,nItemId,pEquipData)
    if not pEquipData then
        return
    end
    local equipsit = pEquipData.equipsit
    if equipsit>0 then
        local strequipsitzi = require "utils.mhsdutils".get_resstring(11692)
        richBox:AppendText(CEGUI.String(strequipsitzi),Equiptipmaker.color_NORMAL_GREEN)
        richBox:AppendBreak()
        local equipsitTable = BeanConfigManager.getInstance():GetTableByName("item.cequipsit"):getRecorder(equipsit)
        if equipsitTable and equipsitTable.id ~= -1 then

            strequipsitzi = equipsitTable.tips
            strequipsitzi = CEGUI.String(strequipsitzi)
            richBox:AppendText(strequipsitzi,Equiptipmaker.color_NORMAL_PURPLE)
            richBox:AppendBreak();
        end
    end

end


function Equiptipmaker.makeEndure(richBox,nItemId,pEquipData)
    if not pEquipData then
        return
    end
    local nEndure = pEquipData.endure
    local strNaijiuzi = require "utils.mhsdutils".get_resstring(11000)
    strNaijiuzi = strNaijiuzi.."："..tostring(nEndure)   --耐久度
    strNaijiuzi = CEGUI.String(strNaijiuzi)
    richBox:AppendText(strNaijiuzi,Equiptipmaker.color_NORMAL_YELLOW)
    richBox:AppendBreak();
end


function Equiptipmaker.makeProducter(richBox,nItemId,pEquipData)
    if not pEquipData then
        return
    end
    local strMakerName = pEquipData.maker
    if not strMakerName then
        return
    end
    if string.len(strMakerName) <= 0 then
        return
    end
    --[[
    local strDzType = ""
    local nType = math.floor( nItemId / 1000000)
    if nType == 5 then
        strDzType =  require "utils.mhsdutils".get_resstring(11004);
    elseif nType == 6 then
        strDzType =  require "utils.mhsdutils".get_resstring(11005);
    end

    if string.len(strDzType) <= 0 then
        return
    end
    --]]
    local strZhizuozhezi = require "utils.mhsdutils".get_resstring(11269)

    strZhizuozhezi = strZhizuozhezi.." "..strMakerName

    strZhizuozhezi = CEGUI.String(strZhizuozhezi)
    richBox:AppendText(strZhizuozhezi, Equiptipmaker.color_NORMAL_YELLOW)
    richBox:AppendBreak();
end

function Equiptipmaker.makeScore(richBox,nItemId,pEquipData)
    if not pEquipData then
        return
    end

    local nScore = pEquipData.equipscore
    local strPingFen =  require "utils.mhsdutils".get_resstring(111)
    strPingFen = strPingFen..tostring(nScore)

    strPingFen = CEGUI.String(strPingFen)
    richBox:AppendText(strPingFen, Equiptipmaker.color_NORMAL_YELLOW)
    richBox:AppendBreak();
end

function Equiptipmaker.makeSex(richBox,nItemId,pEquipData)
    --  m_Tipscontainer.AppendText(sexneed == 1 ? MHSD_UTILS::GETSTRING(132) : MHSD_UTILS::GETSTRING(133), gGetDataManager()->GetMainCharacterData().sex != sexneed ? CEGUI::ColourRect(0xFFFF0000) : CEGUI::ColourRect(NORMAL_YELLOW));
    --	m_Tipscontainer.AppendBreak();
    local colorNormal = Equiptipmaker.color_NORMAL_YELLOW
    local colorRed = CEGUI.ColourRect(CEGUI.PropertyHelper:stringToColour("ffff0000"))  --角色不匹配名字亮红色
    local equipEffectTable = GameTable.item.GetCEquipEffectTableInstance():getRecorder(nItemId)

    local vcnRoleId = equipEffectTable.roleNeed
    if vcnRoleId:size() > 0 then
        local nRoleId = vcnRoleId[0]
        local strRoleEquipzi = require "utils.mhsdutils".get_resstring(11349)
        if not strRoleEquipzi then
            return
        end
        --strRoleEquipzi = "111=$parameter$=222" --wangbin test
        local roleTable = BeanConfigManager.getInstance():GetTableByName("role.createroleconfig"):getRecorder(nRoleId)
        if roleTable.id == -1 then
            return
        end

        local roleColor = colorNormal
        if gGetDataManager() then
            local nModelId = gGetDataManager():GetMainCharacterShape()
            local nCurRoleId = nModelId%10
            if nCurRoleId ~= nRoleId then
                roleColor = colorRed
            end
        end
        local strRoleName1 =""
        if vcnRoleId:size() > 1 then
            strRoleName1 = BeanConfigManager.getInstance():GetTableByName("role.createroleconfig"):getRecorder(vcnRoleId[1]).name
        end
        local strRoleName = roleTable.name
        local strSexNamezi = ""
        local strZhizuozhezi = require "utils.mhsdutils".get_resstring(11349)
        strZhizuozhezi = strZhizuozhezi..":"..strRoleName.." "..strRoleName1
        strSexNamezi = CEGUI.String(strZhizuozhezi)
        richBox:AppendText(strSexNamezi, roleColor)
        richBox:AppendBreak()
        return


    end

    local nNeedSex = equipEffectTable.sexNeed
    --local mainRoleData = gGetDataManager():GetMainCharacterData()
    --local nCurSex = mainRoleData.sex

    if nNeedSex == 0 then
        return
    end
    local strSexNamezi = ""
    if nNeedSex == 1 then
        strSexNamezi = require "utils.mhsdutils".get_resstring(132)
    else
        strSexNamezi = require "utils.mhsdutils".get_resstring(133)
    end

    local roleColor = colorNormal
    if gGetDataManager() then
        local myData = gGetDataManager():GetMainCharacterData()
        if myData.sex ~= nNeedSex then
            roleColor = colorRed
        end
    end


    strSexNamezi = CEGUI.String(strSexNamezi)
    richBox:AppendText(strSexNamezi, roleColor)
    richBox:AppendBreak()

end

--����������ְҵ����
function Equiptipmaker.makeCareer(richBox,nItemId,pEquipData)
    local colorRed = CEGUI.ColourRect(CEGUI.PropertyHelper:stringToColour("ffff0000"))
    local colorNormal = Equiptipmaker.color_NORMAL_YELLOW
    local mainCareedId = gGetDataManager():GetMainCharacterSchoolID()
    local roleColor = colorRed
    local equipEffectTable = GameTable.item.GetCEquipEffectTableInstance():getRecorder(nItemId)
    if not equipEffectTable then
        print("get equipEffectTable failed")
        return
    end
    --����������ֱ��return
    if equipEffectTable.eequiptype ~= 0 then
        return
    end

    local careerIds = StringBuilder.Split( equipEffectTable.needCareer, ";")
    for k,v in pairs(careerIds) do
        if v == tostring(mainCareedId) then
            roleColor = colorNormal
            break
        end
    end
    local career = ""
    for k,v in pairs(careerIds) do
        local schoolinfo = BeanConfigManager.getInstance():GetTableByName("role.schoolinfo"):getRecorder(tonumber(v))
        if schoolinfo then
            career = schoolinfo.name.." "..career
        end
    end

    local strSexNamezi = ""
    local strZhizuozhezi = require "utils.mhsdutils".get_resstring(11431)
    strZhizuozhezi = strZhizuozhezi

    strSexNamezi = CEGUI.String(strZhizuozhezi)
    richBox:AppendText(strSexNamezi, roleColor)
    richBox:AppendBreak()
end

return Equiptipmaker
