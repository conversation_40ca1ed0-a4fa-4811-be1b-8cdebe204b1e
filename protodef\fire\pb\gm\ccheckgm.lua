require "utils.tableutil"
CCheckGM = {}
CCheckGM.__index = CCheckGM



CCheckGM.PROTOCOL_TYPE = 791436

function CCheckGM.Create()
	print("enter CCheckGM create")
	return CCheckGM:new()
end
function CCheckGM:new()
	local self = {}
	setmetatable(self, CCheckGM)
	self.type = self.PROTOCOL_TYPE
	return self
end
function CCheckGM:encode()
	local os = FireNet.Marshal.OctetsStream:new()
	os:compact_uint32(self.type)
	local pos = self:marshal(nil)
	os:marshal_octets(pos:getdata())
	pos:delete()
	return os
end
function CCheckGM:marshal(ostream)
	local _os_ = ostream or FireNet.Marshal.OctetsStream:new()
	return _os_
end

function CCheckGM:unmarshal(_os_)
	return _os_
end

return CCheckGM
