require "logic.dialog"

FuctionCollection = { }
setmetatable(FuctionCollection, Dialog)
FuctionCollection.__index = FuctionCollection

local viplevel = gGetDataManager():GetVipLevel() --判断自身VIP等级
local needlvel = tonumber(GameTable.common.GetCCommonTableInstance():getRecorder(492).value)
local param = {needlvel}
function FuctionCollection.getInstance()
    if not _instance then
        _instance = FuctionCollection:new()
        _instance:OnCreate()
    end
    return _instance
end

function FuctionCollection.getInstanceAndShow()
    if not _instance then
        _instance = FuctionCollection:new()
        _instance:OnCreate()
    else
        _instance:SetVisible(true)
    end
    return _instance
end

function FuctionCollection.getInstanceNotCreate()
    return _instance
end

function FuctionCollection.DestroyDialog()
    if _instance then
        if not _instance.m_bCloseIsHide then
            _instance:OnClose()
            _instance = nil
        else
            _instance:ToggleOpenClose()
        end
    end
end

function FuctionCollection.ToggleOpenClose()
    if not _instance then
        _instance = FuctionCollection:new()
        _instance:OnCreate()
    else
        if _instance:IsVisible() then
            _instance:SetVisible(false)
        else
            _instance:SetVisible(true)
        end
    end
end

function FuctionCollection.GetLayoutFileName()
    return "messageboxfuwul.layout"
end

function FuctionCollection:new()
    local self = { }
    self = Dialog:new()
    setmetatable(self, FuctionCollection)
    return self
end

function FuctionCollection:OnCreate()
    Dialog.OnCreate(self)
    local winMgr = CEGUI.WindowManager:getSingleton();
    -- self.speedFuncBtn:subscribeEvent("MouseClick", self.JumpNpc, self))
    -- self.speedFuncBtn:setID(1)
    self.FunMap = {
        [1] = FuctionCollection.ZhuanhuanZhiye,
        [2] = FuctionCollection.ZhuanhuanBaoshi,
        [3] = FuctionCollection.EquipDianhua,
        [4] = FuctionCollection.EquipRonglian,
        [5] = FuctionCollection.ShengjieEquip,
        [6] = FuctionCollection.EquipChongzhu,
        [7] = FuctionCollection.EquipShengjie,
        [8] = FuctionCollection.BackGame,
    }
    self.btnTbl = {}
    for i=1, #self.FunMap do
        local tempBtn = CEGUI.toPushButton(winMgr:getWindow(string.format("messageboxfuwu/bg/messageboxfuwu/bt%d",i)))
        self.btnTbl[i] = tempBtn
        self.btnTbl[i]:subscribeEvent("Clicked", self.HandleFuncClick, self)
        self.btnTbl[i]:setID(i)
        self.btnTbl[i]:setVisible(true)
    end
    -- self:SetOterFuncion();
end

function FuctionCollection:SetOterFuncion()
    local tableConfig = BeanConfigManager.getInstance():GetTableByName("npc.CFunctionCollection")
    local allId = tableConfig:getAllID()
    local tempTbl = {}
    local lastId = 0
    for i=1, #allId do
        local data = tableConfig:getRecorder(i)
        local tempdata = {}
        if data ~= nil and data.isOpen == true then
            lastId = lastId + 1
            tempdata.originId = i
            tempdata.newId = lastId
            table.insert(tempTbl,tempdata)
        else
            self.btnTbl[i]:setVisible(false)    
        end
    end
    local initX = self.btnTbl[1]:getPosition().x.offset
    local initY = self.btnTbl[1]:getPosition().y.offset
    local stepX = self.btnTbl[2]:getPosition().x.offset - self.btnTbl[1]:getPosition().x.offset
    local stepY = self.btnTbl[10]:getPosition().y.offset - self.btnTbl[1]:getPosition().y.offset
    local row = 4
    local col =  tonumber(math.ceil(#tempTbl/row))
    for i = 1,row do
        for j = 1,col do
            local value = tempTbl[i]
            local newXPos = initX + (i-1) * stepX
            local newYPos = initY + (j-1) * stepY
            self.btnTbl[value.originId]:setPosition(CEGUI.UVector2(CEGUI.UDim(0,newXPos), CEGUI.UDim(0,newYPos)))    
        end
    end
end

function FuctionCollection:HandleFuncClick(args)
    local e = CEGUI.toWindowEventArgs(args)
    local id = e.window:getID()
    self.FunMap[id]();
end

-- 装备点化
function FuctionCollection.ZhuanhuanZhiye()
    require"logic.workshop.Attunement".getInstanceAndShow()
    FuctionCollection.DestroyDialog()
end


-- 装备洗练
function FuctionCollection.ZhuanhuanBaoshi()
    require"logic.workshop.workshopxilian".getInstanceAndShow()
    FuctionCollection.DestroyDialog()
end

--装备进阶
function FuctionCollection.EquipDianhua()
    require"logic.workshop.workshopaq".getInstanceAndShow()
    FuctionCollection.DestroyDialog()
end

--公告
function FuctionCollection.EquipRonglian()
    IOS_MHSD_UTILS.OpenURL("https://docs.qq.com/doc/DUGZ5SldVelBBbmt4");--游戏攻略地址
    FuctionCollection.DestroyDialog()
end

-- 装备熔炼
function FuctionCollection.ShengjieEquip()
    require"logic.workshop.zhuangbeiqh".getInstanceAndShow()
    FuctionCollection.DestroyDialog()
end


function FuctionCollection.EquipChongzhu()
    debugrequire"logic.zhuanzhi.equipupgradedlg".getInstanceAndShow()
    FuctionCollection.DestroyDialog()
end
--备用
function FuctionCollection.EquipShengjie()
      require("logic.redpack.redpacklabel")
    RedPackLabel.DestroyDialog()
    RedPackLabel.show(1)

    FuctionCollection.DestroyDialog()
end

-- 返回游戏
function FuctionCollection.BackGame()
    FuctionCollection.DestroyDialog()
end

function FuctionCollection.getNpcData(NPCID)
    local npcConfig = BeanConfigManager.getInstance():GetTableByName("npc.cnpcconfig"):getRecorder(NPCID);
    if not npcConfig then
        print("[Table Error]:未找到NPC数据--NPCID ",NPCID)
    end
    GetMainCharacter():FlyOrWarkToPos(npcConfig.mapid, 269, 210, NPCID)
    FuctionCollection.DestroyDialog()
end
return FuctionCollection