require "logic.dialog"

vipfuliditudlg = {}
setmetatable(vipfuliditudlg, Dialog)
vipfuliditudlg.__index = vipfuliditudlg

local _instance
local viplevel = gGetDataManager():GetVipLevel() --判断自身VIP等级
function vipfuliditudlg.getInstance()
	if not _instance then
		_instance = vipfuliditudlg:new()
		_instance:OnCreate()
	end
	return _instance
end

function vipfuliditudlg.getInstanceAndShow()
	if not _instance then
		_instance = vipfuliditudlg:new()
		_instance:OnCreate()
	else
		_instance:SetVisible(true)
	end
	return _instance
end

function vipfuliditudlg.getInstanceNotCreate()
	return _instance
end

function vipfuliditudlg.DestroyDialog()
	if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
end

function vipfuliditudlg.ToggleOpenClose()
	if not _instance then
		_instance = vipfuliditudlg:new()
		_instance:OnCreate()
	else
		if _instance:IsVisible() then
			_instance:SetVisible(false)
		else
			_instance:SetVisible(true)
		end
	end
end

function vipfuliditudlg.GetLayoutFileName()
	return "vipzhuanshuditu1.layout"
end

function vipfuliditudlg:new()
	local self = {}
	self = Dialog:new()
	setmetatable(self, vipfuliditudlg)
	return self
end

function vipfuliditudlg:OnCreate()
	Dialog.OnCreate(self)
	local winMgr = CEGUI.WindowManager:getSingleton()

    self.m_btnFanhui = CEGUI.toPushButton(winMgr:getWindow("vipzhuanshuditu/fanhui"))
    self.m_btnVip3 = CEGUI.toPushButton(winMgr:getWindow("vipzhuanshuditu/v3"))
    self.m_btnVip4 = CEGUI.toPushButton(winMgr:getWindow("vipzhuanshuditu/v4"))
    self.m_btnVip5 = CEGUI.toPushButton(winMgr:getWindow("vipzhuanshuditu/v5"))
    self.m_btnVip6 = CEGUI.toPushButton(winMgr:getWindow("vipzhuanshuditu/v6"))
    self.m_btnVip7 = CEGUI.toPushButton(winMgr:getWindow("vipzhuanshuditu/v7"))
    self.m_btnVip8 = CEGUI.toPushButton(winMgr:getWindow("vipzhuanshuditu/v8"))
    self.m_btnVip9 = CEGUI.toPushButton(winMgr:getWindow("vipzhuanshuditu/v9"))
    self.m_btnVip10 = CEGUI.toPushButton(winMgr:getWindow("vipzhuanshuditu/v10"))	
    self.m_btnVip11 = CEGUI.toPushButton(winMgr:getWindow("vipzhuanshuditu/v11"))	
	self.m_btnguanbi = CEGUI.toPushButton(winMgr:getWindow("vipzhuanshuditu/back"))
	
	
	
    self.m_btnFanhui:subscribeEvent("Clicked", vipfuliditudlg.handleQuitBtnClicked, self)
    self.m_btnVip3:subscribeEvent("Clicked", vipfuliditudlg.VIP3jinru, self) 
    self.m_btnVip4:subscribeEvent("Clicked", vipfuliditudlg.VIP4jinru, self)
	self.m_btnVip5:subscribeEvent("Clicked", vipfuliditudlg.VIP5jinru, self)
	self.m_btnVip6:subscribeEvent("Clicked", vipfuliditudlg.VIP6jinru, self)
	self.m_btnVip7:subscribeEvent("Clicked", vipfuliditudlg.VIP7jinru, self)
	self.m_btnVip8:subscribeEvent("Clicked", vipfuliditudlg.VIP8jinru, self)
	self.m_btnVip9:subscribeEvent("Clicked", vipfuliditudlg.VIP9jinru, self)
	self.m_btnVip10:subscribeEvent("Clicked", vipfuliditudlg.VIP10jinru, self)
	self.m_btnVip11:subscribeEvent("Clicked", vipfuliditudlg.VIP11jinru, self)
	self.m_btnguanbi:subscribeEvent("Clicked", vipfuliditudlg.handleQuitBtnClicked, self)
    --self:GetWindow():subscribeEvent("ZChanged", vipfuliditudlg.handleZchange, self)
	
	
	
	
    self.m_text = winMgr:getWindow("vipzhuanshuditu/text")
    self.movingToFront = false
    self:refreshbtn()
end
function vipfuliditudlg:refreshbtn()
    local funopenclosetype = require("protodef.rpcgen.fire.pb.funopenclosetype"):new()
    local manager = require "logic.pointcardserver.pointcardservermanager".getInstanceNotCreate()
    if manager then
        if manager.m_OpenFunctionList.info then
            for i,v in pairs(manager.m_OpenFunctionList.info) do
                if v.key == funopenclosetype.FUN_CHECKPOINT then
                    if v.state == 1 then
                        self.m_btnBuyOrSell:setVisible(false)
                        self.m_text:setText(MHSD_UTILS.get_resstring(11594))
                        break
                    end
                end
            end
        end
    end
end
function vipfuliditudlg:handleZchange(e)
    if not self.movingToFront then
        self.movingToFront = true
        if self:GetWindow():getParent() then
            local drawList = self:GetWindow():getParent():getDrawList()
            if drawList:size() > 0 then
                local topWnd = drawList[drawList:size()-1]
                local wnd = tolua.cast(topWnd, "CEGUI::Window")
                if wnd:getName() == "NewsWarn" then
                    if drawList:size() > 2 then
                        local secondWnd = drawList[drawList:size()-1]
                        self:GetWindow():getParent():bringWindowAbove(self:GetWindow(), tolua.cast(secondWnd, "CEGUI::Window"))
                    end
                else
                    self:GetWindow():getParent():bringWindowAbove(self:GetWindow(), tolua.cast(topWnd, "CEGUI::Window"))
                end
                
            end
        end
        self.movingToFront = false
    end
end
function vipfuliditudlg:handleQuitBtnClicked(e)
if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
end
function vipfuliditudlg:VIP3jinru(e) --V3进入地图

   if viplevel <= 2 then--判断自身VIP等级小于等于2时 提示VIP等级不足
      GetChatManager():AddTipsMsg(192232)
    end
	
	if viplevel >= 0 then--判断自身VIP等级为3时 传送至某地图坐标
    gGetNetConnection():send(fire.pb.mission.CReqGoto(9019, 50, 50));
	GetChatManager():AddTipsMsg(192234)
    end
vipfuliditudlg:handleQuitBtnClicked(e)
end
function vipfuliditudlg:VIP4jinru(e) --V4进入地图

   if viplevel <= 0 then---判断自身VIP等级小于等于3时 提示VIP等级不足
      GetChatManager():AddTipsMsg(192232)
    end
	
	if viplevel >= 0 then--判断自身VIP等级为4时 传送至某地图坐标
    gGetNetConnection():send(fire.pb.mission.CReqGoto(9041, 50, 50));
	GetChatManager():AddTipsMsg(192234)
    end
vipfuliditudlg:handleQuitBtnClicked(e)
end
function vipfuliditudlg:VIP5jinru(e) --V5进入地图

   if viplevel <= 4 then--判断自身VIP等级小于等于4时 提示VIP等级不足
      GetChatManager():AddTipsMsg(192232)
    end
	
	if viplevel >= 0 then--判断自身VIP等级为5时 传送至某地图坐标
    gGetNetConnection():send(fire.pb.mission.CReqGoto(9021, 63, 219));
	GetChatManager():AddTipsMsg(192234)
    end
vipfuliditudlg:handleQuitBtnClicked(e)
end
function vipfuliditudlg:VIP6jinru(e) --V6进入地图

   if viplevel <= 5 then--判断自身VIP等级小于等于5时 提示VIP等级不足
      GetChatManager():AddTipsMsg(192232)
    end
	
	if viplevel >= 6 then--判断自身VIP等级为6时 传送至某地图坐标
    gGetNetConnection():send(fire.pb.mission.CReqGoto(6003, 50, 50));
	GetChatManager():AddTipsMsg(192234)
    end
vipfuliditudlg:handleQuitBtnClicked(e)
end
function vipfuliditudlg:VIP7jinru(e) --V7进入地图

   if viplevel <= 6 then--判断自身VIP等级小于等于6时 提示VIP等级不足
      GetChatManager():AddTipsMsg(192232)
    end
	
	if viplevel >= 7 then--判断自身VIP等级为7时 传送至某地图坐标
    gGetNetConnection():send(fire.pb.mission.CReqGoto(6004, 50, 50));
	GetChatManager():AddTipsMsg(192234)
    end
vipfuliditudlg:handleQuitBtnClicked(e)
end
function vipfuliditudlg:VIP8jinru(e) --V8进入地图

   if viplevel <= 7 then--判断自身VIP等级小于等于7时 提示VIP等级不足
      GetChatManager():AddTipsMsg(192232)
    end
	
	if viplevel >= 8 then--判断自身VIP等级为8时 传送至某地图坐标
    gGetNetConnection():send(fire.pb.mission.CReqGoto(6005, 50, 50));
	GetChatManager():AddTipsMsg(192234)
    end
vipfuliditudlg:handleQuitBtnClicked(e)
end
function vipfuliditudlg:VIP9jinru(e) --V9进入地图

   if viplevel <= 8 then--判断自身VIP等级小于等于8时 提示VIP等级不足
      GetChatManager():AddTipsMsg(192232)
    end
	
	if viplevel >= 9 then--判断自身VIP等级为9时 传送至某地图坐标
    gGetNetConnection():send(fire.pb.mission.CReqGoto(6006, 50, 50));
	GetChatManager():AddTipsMsg(192234)
    end
vipfuliditudlg:handleQuitBtnClicked(e)
end
function vipfuliditudlg:VIP10jinru(e) --V10进入地图

   if viplevel <= 9 then--判断自身VIP等级小于等于9时 提示VIP等级不足
      GetChatManager():AddTipsMsg(192232)
    end
	
	if viplevel >= 10 then--判断自身VIP等级为10时 传送至某地图坐标
    gGetNetConnection():send(fire.pb.mission.CReqGoto(6007, 50, 50));
	GetChatManager():AddTipsMsg(192234)
    end
vipfuliditudlg:handleQuitBtnClicked(e)
end
function vipfuliditudlg:VIP11jinru(e) --V11进入地图

   if viplevel <= 10 then--判断自身VIP等级小于等于10时 提示VIP等级不足
      GetChatManager():AddTipsMsg(192232)
    end
	
	if viplevel >= 11 then--判断自身VIP等级为11时 传送至某地图坐标
    gGetNetConnection():send(fire.pb.mission.CReqGoto(6008, 50, 50));
	GetChatManager():AddTipsMsg(192234)
    end
vipfuliditudlg:handleQuitBtnClicked(e)
end


function vipfuliditudlg:Show()
    self:GetWindow():setVisible(true)
end
return vipfuliditudlg