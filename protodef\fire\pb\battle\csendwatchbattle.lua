require "utils.tableutil"
CSendWatchBattle = {}
CSendWatchBattle.__index = CSendWatchBattle



CSendWatchBattle.PROTOCOL_TYPE = 793443

function CSendWatchBattle.Create()
	print("enter CSendWatchBattle create")
	return CSendWatchBattle:new()
end
function CSendWatchBattle:new()
	local self = {}
	setmetatable(self, CSendWatchBattle)
	self.type = self.PROTOCOL_TYPE
	self.roleid = 0

	return self
end
function CSendWatchBattle:encode()
	local os = FireNet.Marshal.OctetsStream:new()
	os:compact_uint32(self.type)
	local pos = self:marshal(nil)
	os:marshal_octets(pos:getdata())
	pos:delete()
	return os
end
function CSendWatchBattle:marshal(ostream)
	local _os_ = ostream or FireNet.Marshal.OctetsStream:new()
	_os_:marshal_int64(self.roleid)
	return _os_
end

function CSendWatchBattle:unmarshal(_os_)
	self.roleid = _os_:unmarshal_int64()
	return _os_
end

return CSendWatchBattle
