require "utils.tableutil"
SDailyPay = {}
SDailyPay.__index = SDailyPay



SDailyPay.PROTOCOL_TYPE = 817957

function SDailyPay.Create()
	print("enter SDailyPay create")
	return SDailyPay:new()
end
function SDailyPay:new()
	local self = {}
	setmetatable(self, SDailyPay)
	self.type = self.PROTOCOL_TYPE
	self.rewardlv = 0
	self.dangripay = 0
	self.rewardmap = {}

	return self
end
function SDailyPay:encode()
	local os = FireNet.Marshal.OctetsStream:new()
	os:compact_uint32(self.type)
	local pos = self:marshal(nil)
	os:marshal_octets(pos:getdata())
	pos:delete()
	return os
end
function SDailyPay:marshal(ostream)
	local _os_ = ostream or FireNet.Marshal.OctetsStream:new()
	_os_:marshal_int32(self.rewardlv)
	_os_:marshal_int64(self.dangripay)

	----------------marshal map
	_os_:compact_uint32(TableUtil.tablelength(self.rewardmap))
	for k,v in pairs(self.rewardmap) do
		_os_:marshal_int32(k)
		_os_:marshal_int64(v)
	end

	return _os_
end

function SDailyPay:unmarshal(_os_)
	self.rewardlv = _os_:unmarshal_int32()
	self.dangripay = _os_:unmarshal_int64()
	----------------unmarshal map
	local sizeof_rewardmap=0,_os_null_rewardmap
	_os_null_rewardmap, sizeof_rewardmap = _os_: uncompact_uint32(sizeof_rewardmap)
	for k = 1,sizeof_rewardmap do
		local newkey, newvalue
		newkey = _os_:unmarshal_int32()
		newvalue = _os_:unmarshal_int64()
		self.rewardmap[newkey] = newvalue
	end
	return _os_
end

return SDailyPay
