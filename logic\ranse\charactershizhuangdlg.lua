require "logic.dialog"

CharacterShiZhuangDlg = {}
setmetatable(CharacterShiZhuangDlg, Dialog)
CharacterShiZhuangDlg.__index = CharacterShiZhuangDlg

local _instance
function CharacterShiZhuangDlg.getInstance()
    if not _instance then
        _instance = CharacterShiZhuangDlg:new()
        _instance:OnCreate()
    end
    return _instance
end

function CharacterShiZhuangDlg.getInstanceAndShow()
    if not _instance then
        _instance = CharacterShiZhuangDlg:new()
        _instance:OnCreate()
    else
        _instance:SetVisible(true)
    end
    return _instance
end

function CharacterShiZhuangDlg.getInstanceNotCreate()
    return _instance
end

function CharacterShiZhuangDlg.DestroyDialog()
    if _instance then
        if not _instance.m_bCloseIsHide then
            _instance:OnClose()
            _instance = nil
        else
            _instance:ToggleOpenClose()
        end
    end
end

function CharacterShiZhuangDlg.ToggleOpenClose()
    if not _instance then
        _instance = CharacterShiZhuangDlg:new()
        _instance:OnCreate()
    else
        if _instance:IsVisible() then
            _instance:SetVisible(false)
        else
            _instance:SetVisible(true)
        end
    end
end

function CharacterShiZhuangDlg.GetLayoutFileName()
    return "jueseshizhuang.layout"
end

function CharacterShiZhuangDlg:new()
    local self = {}
    self = Dialog:new()
    setmetatable(self, CharacterShiZhuangDlg)
    return self
end

function CharacterShiZhuangDlg:OnCreate()
    Dialog.OnCreate(self)
    local winMgr = CEGUI.WindowManager:getSingleton()

    SetPositionOfWindowWithLabel(self:GetWindow())
    self:GetCloseBtn():removeEvent("Clicked")
    self:GetCloseBtn():subscribeEvent("Clicked", RanSeLabel.DestroyDialog, nil)

    self.leftDown = false;
    self.rightDown = false;
    self.downTime = 0;
    self.turnL = CEGUI.toPushButton(winMgr:getWindow("jueseshizhuang/xuanniu"));
    self.turnR = CEGUI.toPushButton(winMgr:getWindow("jueseshizhuang/xuanniu2"));
	self.m_btnguanbi = CEGUI.toPushButton(winMgr:getWindow("jueseshizhuang/back"))
    self.cbTipBtn0 = CEGUI.toPushButton(winMgr:getWindow("jueseshizhuang/yichu3"))--我的衣橱
    self.cbTipBtn0:subscribeEvent("Clicked", self.handleCombineTipClicked0, self)

    self.cbTipBtn1 = CEGUI.toPushButton(winMgr:getWindow("jueseshizhuang/yichu1"))--我的衣橱
	self.cbTipBtn1:subscribeEvent("Clicked", self.handleCombineTipClicked1, self)
	
	
	self.cbTipBtn2 = CEGUI.toPushButton(winMgr:getWindow("jueseshizhuang/yichu2"))--宠物染色
	self.cbTipBtn2:subscribeEvent("Clicked", self.handleCombineTipClicked2, self)
	
	self.cbTipBtn8 = CEGUI.toPushButton(winMgr:getWindow("jueseshizhuang/yichu8"))--宠物染色
	self.cbTipBtn8:subscribeEvent("Clicked", self.handleCombineTipClicked8, self)

    self.turnL:subscribeEvent("MouseButtonDown", CharacterShiZhuangDlg.handleLeftClicked, self)
    self.turnR:subscribeEvent("MouseButtonDown", CharacterShiZhuangDlg.handleRightClicked, self)
    self.turnL:subscribeEvent("MouseButtonUp", CharacterShiZhuangDlg.handleLeftUp, self)
    self.turnR:subscribeEvent("MouseButtonUp", CharacterShiZhuangDlg.handleRightUp, self)
    self.turnL:subscribeEvent("MouseLeave", CharacterShiZhuangDlg.handleLeftUp, self)
    self.turnR:subscribeEvent("MouseLeave", CharacterShiZhuangDlg.handleRightUp, self)
    self.m_btnguanbi:subscribeEvent("Clicked", CharacterShiZhuangDlg.handleQuitBtnClicked, self)	

    self.shiyongBtn = CEGUI.toPushButton(winMgr:getWindow("jueseshizhuang/huanyuan111"))--宠物染色
    self.shiyongBtn:subscribeEvent("Clicked", CharacterShiZhuangDlg.handleShiYongClicked, self)
    self.goumaiBtn = CEGUI.toPushButton(winMgr:getWindow("jueseshizhuang/huanyuan11"))--宠物染色
    self.goumaiBtn:subscribeEvent("Clicked", CharacterShiZhuangDlg.handleGouMaiClicked, self)
    self.shiyongBtn:setVisible(false)
    --self.goumaiBtn:setVisible(false)


    self.shichuan = CEGUI.toPushButton(winMgr:getWindow("jueseshizhuang/biaoti/qiehuan"));
    self.shichuan:subscribeEvent("Clicked", CharacterShiZhuangDlg.handleShiChuanClicked, self)
    self.shichuan:EnableClickAni(false)
    self.yongyou = CEGUI.toPushButton(winMgr:getWindow("jueseshizhuang/biaoti/qiehuan1"));
    self.yongyou:subscribeEvent("Clicked", CharacterShiZhuangDlg.handleYongYouClicked, self)
    self.yongyou:EnableClickAni(false)




    local data = gGetDataManager():GetMainCharacterData()
    self.dir = Nuclear.XPDIR_BOTTOMRIGHT;
    self.canvas = winMgr:getWindow("jueseshizhuang/beijing/moxing")
    self.sprite = gGetGameUIManager():AddWindowSprite(self.canvas, data.shape, self.dir, 0, 0, true)
    self.partList = {};
    self.partList[1] = {}
    self.partList[2] = {}
    self.colorList = {};
    self.colorList[1] = {}
    self.colorList[2] = {}
    local ids = BeanConfigManager.getInstance():GetTableByName("role.crolercolorconfig"):getAllID()
    local num = table.getn(ids)
    for i = 1, num do
        if ids[i] < 1000 then
            local record = BeanConfigManager.getInstance():GetTableByName("role.crolercolorconfig"):getRecorder(ids[i])
            table.insert(self.partList[record.rolepos], record.id)
            --colorlist 角色1颜色图,角色2颜色图...
            local clr = record.colorlist[data.shape - 1010101]
            --rolepos 部位
            table.insert(self.colorList[record.rolepos], clr)
        end
    end
    self.currentIDA = 1;
    self.currentIDB = 1;

    self.ItemCellNeedItem1 = CEGUI.toItemCell(winMgr:getWindow("jueseshizhuang/ranliao1"))

    self.ItemCellNeedItem1:setVisible(false)


    self.neeItemCountText1 = winMgr:getWindow("jueseshizhuang/ranliaoshu1")

    self.neeItemCountText1:setText("")

    self.neeItemNameText1 = winMgr:getWindow("jueseshizhuang/ranliaoming1")

    self.neeItemNameText1:setText("")
    self.select = 0;
    self.selectye = 0;
    self.ItemCellNeedItem1:setVisible(false)
    self.neeItemNameText1:setVisible(false)
    self.neeItemCountText1:setVisible(false)
    local ids =BeanConfigManager.getInstance():GetTableByName("item.cshizhuangyichu"):getAllID()
    self.szlistWnd = CEGUI.toScrollablePane(winMgr:getWindow("jueseshizhuang/biaoti/shizhuang/szs"));
    self.szlistWnd:EnableHorzScrollBar(false)
    self:refreshSzTable()
end
function CharacterShiZhuangDlg:handleShiYongClicked(args)
    if self.select~=0 then
        local data = gGetDataManager():GetMainCharacterData()
        local cmd = require "protodef.fire.pb.shizhuang.cchangeshizhuangshiyong".Create()
        cmd.shizhuangid = self.select
        cmd.moxing = data.shape
        LuaProtocolManager.getInstance():send(cmd)
    end
end
function CharacterShiZhuangDlg:handleGouMaiClicked(args)
    if self.select~=0 then
        local cmd = require "protodef.fire.pb.shizhuang.cchangeshizhuanggoumai".Create()
        cmd.shizhuangid = self.select
        LuaProtocolManager.getInstance():send(cmd)
    end
end
function CharacterShiZhuangDlg:handleShiChuanClicked(args)
    local sz = #self.m_szList
    for index  = 1, sz do
        local lyout = self.m_szList[1]
        lyout.addclick = nil
        lyout.LevelText = nil
        self.szlistWnd:removeChildWindow(lyout)
        CEGUI.WindowManager:getSingleton():destroyWindow(lyout)
        table.remove(self.m_szList,1)
    end
    self:refreshSzTable()
    self.ItemCellNeedItem1:setVisible(false)
    self.neeItemNameText1:setVisible(false)
    self.neeItemCountText1:setVisible(false)
    self.ItemCellNeedItem1:setVisible(true)
    self.neeItemNameText1:setVisible(true)
    self.neeItemCountText1:setVisible(true)
    self.shiyongBtn:setVisible(false)
    self.goumaiBtn:setVisible(true)
    self.selectye = 1;
end
function CharacterShiZhuangDlg:handleYongYouClicked(args)
    local sz = #self.m_szList
    for index  = 1, sz do
        local lyout = self.m_szList[1]
        lyout.addclick = nil
        lyout.LevelText = nil
        self.szlistWnd:removeChildWindow(lyout)
        CEGUI.WindowManager:getSingleton():destroyWindow(lyout)
        table.remove(self.m_szList,1)
    end
    self.ItemCellNeedItem1:setVisible(false)
    self.neeItemNameText1:setVisible(false)
    self.neeItemCountText1:setVisible(false)
    self.shiyongBtn:setVisible(true)
    self.goumaiBtn:setVisible(false)
    local cmd = require "protodef.fire.pb.shizhuang.cyichuyongyou".Create()
    cmd.xx=1
    LuaProtocolManager.getInstance():send(cmd)
    self.selectye = 2;
end
function CharacterShiZhuangDlg:refreshSzTable()
    local winMgr = CEGUI.WindowManager:getSingleton()
    local sx = 2.0;
    local sy = 2.0;
    self.m_szList = {}
    local index = 0
    local index2 = 0
    local ids =BeanConfigManager.getInstance():GetTableByName("item.cshizhuangyichu"):getAllID()
    for i = 1, #ids do
        local shizhuang = BeanConfigManager.getInstance():GetTableByName("item.cshizhuangyichu"):getRecorder(i)
        local shapeid = gGetDataManager():GetMainCharacterShape()
        local shapeid1=tostring(shapeid)
        local shape1=string.sub(shapeid1,-2)
        local shapeid2=tostring(shizhuang.moxing)
        local shape2=string.sub(shapeid2,-2)
        if shape1==shape2 then
            local sID = "CharacterShiZhuangDlg" .. tostring(index)
            local lyout = winMgr:loadWindowLayout("jueseshizhuangcell.layout",sID);
            self.szlistWnd:addChildWindow(lyout)
            if index2>=3 then
                index2=0
            end
            lyout:setPosition(CEGUI.UVector2(CEGUI.UDim(0.0, sx + index2 * (lyout:getWidth().offset)), CEGUI.UDim(0.0, sy + math.floor(index/3) * (lyout:getHeight().offset))))
            index2=index2+1

            lyout.addclick =  CEGUI.toGroupButton(winMgr:getWindow(sID.."jueseshizhuangcell"));
            lyout.addclick:setID(shizhuang.id)
            lyout.addclick:subscribeEvent("MouseButtonUp", CharacterShiZhuangDlg.handleSzSelected, self)
            lyout.szCell = CEGUI.toItemCell(winMgr:getWindow(sID.."jueseshizhuangcell/touxiang"))
            local shapeData = BeanConfigManager.getInstance():GetTableByName("npc.cnpcshape"):getRecorder(shizhuang.moxing)
            local image = gGetIconManager():GetImageByID(shizhuang.icon)
            lyout.szCell:SetLockState(false)
            lyout.szCell:SetImage(image)
            lyout.szCell:ClearCornerImage(0)
            lyout.szCell:ClearCornerImage(1)

            table.insert(self.m_szList, lyout)
            index = index + 1
        end
    end
end
function CharacterShiZhuangDlg:refreshSzTable2(szList)

    local winMgr = CEGUI.WindowManager:getSingleton()
    local sx = 2.0;
    local sy = 2.0;
    self.m_szList = {}
    local index = 0
    local index2 = 0
    for k,v in pairs(szList) do
        local shizhuang = BeanConfigManager.getInstance():GetTableByName("item.cshizhuangyichu"):getRecorder(v)
        local sID = "CharacterShiZhuangDlg" .. tostring(index)
        local lyout = winMgr:loadWindowLayout("jueseshizhuangcell.layout",index);
        self.szlistWnd:addChildWindow(lyout)
        if index2>=3 then
            index2=0
        end
        lyout:setPosition(CEGUI.UVector2(CEGUI.UDim(0.0, sx + index2 * (lyout:getWidth().offset)), CEGUI.UDim(0.0, sy + math.floor(index/3) * (lyout:getHeight().offset))))
        index2=index2+1

        lyout.addclick =  CEGUI.toGroupButton(winMgr:getWindow(index.."jueseshizhuangcell"));
        lyout.addclick:setID(shizhuang.id)
        lyout.addclick:subscribeEvent("MouseButtonUp", CharacterShiZhuangDlg.handleSzSelected2, self)
        lyout.szCell = CEGUI.toItemCell(winMgr:getWindow(index.."jueseshizhuangcell/touxiang"))
        local shapeData = BeanConfigManager.getInstance():GetTableByName("npc.cnpcshape"):getRecorder(shizhuang.moxing)
        local image = gGetIconManager():GetImageByID(shizhuang.icon)
        lyout.szCell:SetLockState(false)
        lyout.szCell:SetImage(image)
        lyout.szCell:ClearCornerImage(0)
        lyout.szCell:ClearCornerImage(1)

        table.insert(self.m_szList, lyout)
        index = index + 1
    end
end
function CharacterShiZhuangDlg:handleSzSelected(args)
    local wnd = CEGUI.toWindowEventArgs(args).window
    local cell = CEGUI.toItemCell(wnd)
    local idx = cell:getID()
    local ids =BeanConfigManager.getInstance():GetTableByName("item.cshizhuangyichu"):getAllID()
    if idx <= #ids then
        local shizhuang = BeanConfigManager.getInstance():GetTableByName("item.cshizhuangyichu"):getRecorder(idx)
        self.sprite = gGetGameUIManager():AddWindowSprite(self.canvas, shizhuang.moxing, self.dir, 0, 0, true)
        self.select=idx
            self.ItemCellNeedItem1:setVisible(true)
            self.neeItemNameText1:setVisible(true)
            self.neeItemCountText1:setVisible(true)
            local item = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(shizhuang.cailiao);
            local itemIcon = gGetIconManager():GetItemIconByID(shizhuang.icon)
            self.ItemCellNeedItem1:setVisible(true)
            self.ItemCellNeedItem1:setID(shizhuang.cailiao)
            self.ItemCellNeedItem1:SetImage(itemIcon)
            self.ItemCellNeedItem1:SetTextUnit(shizhuang.cailiaonum)
            SetItemCellBoundColorByQulityItemWithId(self.ItemCellNeedItem1,shizhuang.cailiao)
            self.neeItemNameText1:setText(item.name)
            local roleItemManager = require("logic.item.roleitemmanager").getInstance()
            local mymoney=roleItemManager:GetItemNumByBaseID(shizhuang.cailiao)
            self.neeItemCountText1:setText(shizhuang.cailiaonum.." / "..mymoney)
    end
end
function CharacterShiZhuangDlg:handleSzSelected2(args)
    local wnd = CEGUI.toWindowEventArgs(args).window
    local cell = CEGUI.toItemCell(wnd)
    local idx = cell:getID()
    local ids =BeanConfigManager.getInstance():GetTableByName("item.cshizhuangyichu"):getAllID()
    if idx <= #ids then
        local shizhuang = BeanConfigManager.getInstance():GetTableByName("item.cshizhuangyichu"):getRecorder(idx)
        self.sprite = gGetGameUIManager():AddWindowSprite(self.canvas, shizhuang.moxing, self.dir, 0, 0, true)
        self.select=idx
        self.ItemCellNeedItem1:setVisible(false)
        self.neeItemNameText1:setVisible(false)
        self.neeItemCountText1:setVisible(false)
    end
end
function CharacterShiZhuangDlg:handleQuitBtnClicked(e)
if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
end
function CharacterShiZhuangDlg:handleCombineTipClicked0()--衣橱按钮
    self.DestroyDialog();

    --require("logic.ranse.ranselabel").Show(1)--染色
    require("logic.ranse.ranselabel").Show(1)--衣橱
    --require"logic.workshop.zhuangbeiqh".getInstanceAndShow()
end
function CharacterShiZhuangDlg:handleCombineTipClicked1()--衣橱按钮
          self.DestroyDialog();

	 --require("logic.ranse.ranselabel").Show(1)--染色
	 require("logic.ranse.ranselabel").Show(2)--衣橱
	--require"logic.workshop.zhuangbeiqh".getInstanceAndShow()
end
function CharacterShiZhuangDlg:handleCombineTipClicked2()--宠物按钮
          self.DestroyDialog();

	 --require("logic.ranse.ranselabel").Show(1)--染色
	 --require("logic.ranse.ranselabel").Show(2)--衣橱
	 require("logic.ranse.ranselabel").Show(3)--宠物染色
	--require"logic.workshop.zhuangbeiqh".getInstanceAndShow()
end
function CharacterShiZhuangDlg:handleCombineTipClicked8()--宠物按钮
          self.DestroyDialog();

	 --require("logic.ranse.ranselabel").Show(1)--染色
	 --require("logic.ranse.ranselabel").Show(2)--衣橱
	 require("logic.ranse.ranselabel").Show(3)--宠物染色
	--require"logic.workshop.zhuangbeiqh".getInstanceAndShow()
end

function CharacterShiZhuangDlg:handleLeftClicked(args)
    self.dir = self.dir + 1;
    if self.dir > 7 then
        self.dir = 0;
    end
    self.sprite:SetUIDirection(self.dir)
    self.leftDown = true;
    self.downTime = 0;
end

function CharacterShiZhuangDlg:handleRightClicked(args)
    self.dir = self.dir - 1;
    if self.dir < 0 then
        self.dir = 7;
    end
    self.sprite:SetUIDirection(self.dir)
    self.rightDown = true;
    self.downTime = 0;
end
function CharacterShiZhuangDlg:handleLeftUp(args)
    self.leftDown = false;
end
function CharacterShiZhuangDlg:handleRightUp(args)
    self.rightDown = false;
end
return CharacterShiZhuangDlg
