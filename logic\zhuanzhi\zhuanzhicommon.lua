--region *.lua
--Date
--此文件由[<PERSON><PERSON><PERSON>]插件自动生成

--SchoolID = {
--    { 1010101, 1010103, 1010105 },
--    { 1010102, 1010104, 1010106 }
--}
SchoolID = {
    1010101,
    1010102,
    1010103,
    1010104,
    1010105,
    1010106,
    1010107,
    1010108,
    1010109,
    1010110,
	1010111,
	1010112,
	1010113,
	1010114,
	1010115,
	1010116,
	1010117,
}


--从剑侠客到狐美人1-10,对应可选门派id
ZhiYeStrID = {
    { 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23 }, --yqf
    { 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23 }, --mzb
    { 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23 }, --bwy
    { 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23 }, --xqx
    { 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23 }, --lxt
    { 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23 }, --lsy
    { 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23 }, --cyt
    { 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23 }, --mlf
    { 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23 }, --zyr
    { 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23 }, --cbq
	{ 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23 }, --cbq
	{ 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23 }, --cbq
	{ 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23 }, --cbq
	{ 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23 }, --cbq
	{ 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23 }, --cbq
	{ 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23 }, --cbq
	{ 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23 }, --cbq
}

TableID = {
   1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,
}
--每一级宝石在表中的起始id
BaoShiLvStartID = {
    35001,
    35010,
    35019,
    35028,
    35037,
    35046,
    35055,
    35064,
    35073,
    35082,
    35091,
    35100,
    35109,
    35118,
    35127,
    35136,
    35145,
    35154,
    35163,
    35172,
    35181,
    35190,
    35199,
	35208,
	35217,
	35226,
	35235,
	35244,
	35253,
	35262
}

BaoShiShopStartID = {
    1000,
    1001,
    1002,
    1003,
    1004,
    1005,
    1006,
    1007,
    1008
}

--武器类型 d道具类型表
--[造型id] 武器类型
LastClassForWeaponType = {
    [1] = 12898,
    [2] = 43960,
    [3] = 18090,
    [4] = 30890,
    [5] = 28294,
    [6] = 41094,
    [7] = 43690,
    [8] = 43960,
    [9] = 43690, 
    [10] = 43960,
	[11] = 43960,
	[12] = 43690,
	[13] = 43960,
	[14] = 43960,
	[15] = 43960,
	[16] = 43960,
	[17] = 43960
}

--[造型id] = z装备表武器id的末尾数
ShapeWithWeaponNumber = {
    [1010101] = 01,
    [1010102] = 07,
    [1010103] = 03,
    [1010104] = 06,
    [1010105] = 17,
    [1010106] = 15,
    [1010107] = 02,
    [1010108] = 08,
    [1010109] = 12,
    [1010110] = 13,
	[1010111] = 16,
	[1010112] = 05,
	[1010113] = 09,
	[1010114] = 10,
	[1010115] = 11,
	[1010116] = 04,
	[1010117] = 14 
}

LeftChangeGemTimes = 0
LeftChangeWeaponTimes = 0

ZhuanZhiSex = 0 --当前角色性别

--endregion
