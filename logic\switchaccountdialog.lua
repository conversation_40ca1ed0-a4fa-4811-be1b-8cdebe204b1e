require "logic.dialog"
require "logic.selectserverentry"
require "utils.commonutil"
--require "logic.logintipdlg"
debugrequire "logic.accountlistdlg"

SwitchAccountDialog = {}
setmetatable(SwitchAccountDialog, Dialog)
SwitchAccountDialog.__index = SwitchAccountDialog


------------------- public: -----------------------------------
---- singleton /////////////////////////////////////////------
local _instance;
function SwitchAccountDialog.getInstance()

    if not _instance then
        _instance = SwitchAccountDialog:new()
        _instance:OnCreate()
    end
    
    return _instance
end

function SwitchAccountDialog.getInstanceAndShow()
	print("SwitchAccountDialog show")
    if not _instance then
        _instance = SwitchAccountDialog:new()
        _instance:OnCreate()
	else
		print("set visible")
		_instance:SetVisible(true)
    end
    
    return _instance
end

function SwitchAccountDialog.getInstanceNotCreate()
    return _instance
end

function SwitchAccountDialog.DestroyDialog()
	if _instance then
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
end

function SwitchAccountDialog.ToggleOpenClose()
	if not _instance then 
		_instance = SwitchAccountDialog:new() 
		_instance:OnCreate()
	else
		if _instance:IsVisible() then
			_instance:SetVisible(false)
		else
			_instance:SetVisible(true)
		end
	end
end
----/////////////////////////////////////////------

function SwitchAccountDialog.GetLayoutFileName()
    return "switchaccountdialog.layout"
end

function SwitchAccountDialog:OnCreate()

    Dialog.OnCreate(self)

    local winMgr = CEGUI.WindowManager:getSingleton()
    -- get windows
    self.m_Account = CEGUI.Window.toEditbox(winMgr:getWindow("switchaccount/accountbox"))
    self.m_KeyEdit = CEGUI.Window.toEditbox(winMgr:getWindow("switchaccount/pwdbox"))
    self.m_LoginBtn = CEGUI.Window.toPushButton(winMgr:getWindow("switchaccount/login"));
	self.m_PassengerLoginBtn = CEGUI.Window.toPushButton(winMgr:getWindow("switchaccount/passengerlogin"))
    self.m_pRegisterAccount = CEGUI.Window.toPushButton(winMgr:getWindow("switchaccount/register"));
	self.expandBtn = CEGUI.toPushButton(winMgr:getWindow("switchaccount/accountbg/expandBtn"))
	self.accountBg = winMgr:getWindow("switchaccount/accountbg")

    -- subscribe event
    self.m_LoginBtn:subscribeEvent("Clicked", SwitchAccountDialog.HandleLoginBtnClick, self) 
    self.m_pRegisterAccount:subscribeEvent("Clicked", SwitchAccountDialog.HandleRegisterAccountBtnClick, self) 

    self.m_KeyEdit:subscribeEvent("Activated", SwitchAccountDialog.HandleKeyEditActivate, self) 
    self.m_KeyEdit:subscribeEvent("Deactivated", SwitchAccountDialog.HandleKeyEditDeactivate, self) 
	
	-- self.expandBtn:subscribeEvent("Clicked", SwitchAccountDialog.HandleExpandBtnClick, self)
    
    --init settings
    self.m_KeyEdit:setTextMasked(true);
    self.m_KeyEdit:setMaxTextLength(self.MAX_LENGTH_PASSWORD)

	--self.m_Account:activate()

	self:InitAccountList()
end

------------------- private: -----------------------------------

function SwitchAccountDialog:new()
    local self = {}
    self = Dialog:new()
    setmetatable(self, SwitchAccountDialog)

    self.MAX_LENGTH_PASSWORD = 16

    return self
end

function SwitchAccountDialog:InitAccountList()

	local strLastAccount = gGetLoginManager():GetAccount()
    self.m_Account:setText(strLastAccount)
	self.m_Account:setCaratIndex(#strLastAccount)
    
    local strLastPassword = gGetLoginManager():GetPassword()
    self.m_KeyEdit:setText(strLastPassword)
    
    return true

end

function SwitchAccountDialog:HandleLoginBtnClick(args)
    self:LoginGame()
    return true
end

function SwitchAccountDialog:HandleRegisterAccountBtnClick(args)
    print("register account btn clicked") 
    return true
end

function SwitchAccountDialog:HandleKeyEditActivate(args)
    return true
end

function SwitchAccountDialog:HandleKeyEditDeactivate(args)
    return true
end

-- function SwitchAccountDialog:HandleExpandBtnClick(args)
-- 	local dlg = AccountListDlg.toggleShowHide(self.accountBg)
-- 	if dlg then
-- 		dlg:setTriggerBtn(CEGUI.toWindowEventArgs(args).window)
-- 		dlg:GetWindow():setPosition(CEGUI.UVector2(CEGUI.UDim(0,0), CEGUI.UDim(0, self.accountBg:getPixelSize().height+1)))
-- 	end
-- end

function SwitchAccountDialog:LoginGame()

    local account = self.m_Account:getText()
    
    if account == "" then
        GetCTipsManager():AddMessageTipById(144784)
        return true
    end

	print(account)
    local key = self.m_KeyEdit:getText()
	if key == "" then
        GetCTipsManager():AddMessageTipById(144784)
        return true
    end

	print(key)
    local host = gGetLoginManager():GetHost()
	print(host)
    local port = gGetLoginManager():GetPort()
	print(port)
    
    gGetLoginManager():SetAccountInfo(account)
    gGetLoginManager():SetPassword(key)
	
	SetServerIniInfo("Account", "LastAccount", account)
	SetServerIniInfo("Password", "LastPassword", key)

	self.DestroyDialog()
	SelectServerEntry.getInstanceAndShow()
	
    if DeviceInfo:sGetDeviceType()==4 then --WIN7_32
        if gGetLoginManager():isFirstEnter() then
            windowsexplain.getInstanceAndShow()
        end
    end
--	if LoginTipDlg.getInstance() then
--		LoginTipDlg.DestroyDialog()
--	end
--	LoginTipDlg.getInstanceAndShow()

end

return SwitchAccountDialog
