local EQUIPNUM = 31;

CEquipDialog =
{
	m_pEquipCell = { }
};
CEquipDialog.__index = CEquipDialog;

function CEquipDialog:GetWindow()
	return self.m_pParentWindow;
end

function CEquipDialog:SetSelectID(nID)
	self.m_nSelectCellID = nID;
end

function CEquipDialog:GetSelectID()
	return self.m_nSelectCellID;
end

function CEquipDialog:removeEventMapChangeFunctor()
	if (gGetScene() and self.mEventMapChangeFunctor) then
		gGetScene().EventMapChange:RemoveScriptFunctor(self.mEventMapChangeFunctor);
		self.mEventMapChangeFunctor = nil;
	end
end

function CEquipDialog:GetItemTableByPos(pos)
	return nil;
end

function CEquipDialog:showEffectOnEquipCell(nSecondType, nEffectId)
	if (nSecondType >= eEquipType_MAXTYPE) then
		return;
	end

	local pEquipCell = m_pEquipCell[nSecondType];
	if (not pEquipCell) then
		return;
	end
	local bCycle = false;

	local strEffectName = MHSD_UTILS.get_effectpath(nEffectId);
	gGetGameUIManager():AddUIEffect(pEquipCell, strEffectName, bCycle);

end

function CEquipDialog:HandleWindowPosChange(e)
	local pt = self.m_pSpriteBack:GetScreenPosOfCenter();
	local wndHeight = self.m_pSpriteBack:getPixelSize().height;
	local xPos =(pt.x);
	local yPos =(pt.y + wndHeight / 3.0);

	if (self.m_pEquipUISprite) then
		self.m_pEquipUISprite:SetUILocation(Nuclear.NuclearPoint(xPos, yPos));
	end
	return true;
end

function CEquipDialog:UpdataModel()
	if (self.m_pEquipUISprite) then
		self.m_pEquipUISprite:SetUIDirection(Nuclear.XPDIR_BOTTOMRIGHT);
		local info = GetMainCharacter():GetComponentInfo();
		self.m_pEquipUISprite:RefreshSpriteComponent(info);
	end
end

function CEquipDialog:GetEquipTabBackImage(loc)
	if (loc == eEquipType_CUFF) then
		return "Cuff";
	elseif loc == eEquipType_ADORN then
		return "Accessories";
	-- elseif loc == eEquipType_ADORNYUANSHEN then
	-- 	return "Accessories";
	elseif loc == eEquipType_LORICAE then
		return "Armour";
	-- elseif loc == eEquipType_LORICAEYUANSHEN then
	-- 	return "Armour";
	elseif loc == eEquipType_ARMS then
		return "Weapon";
	-- elseif loc == eEquipType_ARMSYUANSHEN then
	-- 	return "Weapon";
	elseif loc == eEquipType_TIRE then
		return "Head";
	-- elseif loc == eEquipType_TIREYUANSHEN then
	-- 	return "Head";
	elseif loc == eEquipType_BOOT then
		return "Shoe";
	-- elseif loc == eEquipType_BOOTYUANSHEN then
	-- 	return "Shoe";
	elseif loc == eEquipType_WAISTBAND then
		return "Belt";
	-- elseif loc == eEquipType_WAISTBANDYUANSHEN then
	-- 	return "Belt";
	elseif loc == eEquipType_EYEPATCH then
	elseif loc == eEquipType_RESPIRATOR then
	elseif loc == eEquipType_VEIL then
	elseif loc == eEquipType_CLOAK then
		return "Mask";
	elseif loc == eEquipType_ERSHI then
	elseif loc == eEquipType_CHIBANG then
	elseif loc == eEquipType_FABAOA then
	elseif loc == eEquipType_FABAOB then
	elseif loc == eEquipType_JINYI then
	elseif loc == eEquipType_FASHION then
		return "Fashion";
	else
	end
	return "";
end

--[[
function CEquipDialog:HandleShiftClickItem(pItem)
	if (GetChatManager() and pItem and pItem:GetObject()) then
		local ItemColor = CEGUI.colour(pItem:GetLinkTipsColor());
		local bind = pItem:GetObject().data.flags;
		local loseeffecttime = pItem:GetObject().data.loseeffecttime;
		GetChatManager():AddObjectTipsLinkToCurInputBox(pItem:GetName(), gGetDataManager():GetMainCharacterID(), fire.pb.talk.ShowInfo.SHOW_ITEM, pItem:GetThisID(), pItem:GetBaseObject().id, 0, fire.pb.item.BagTypes.EQUIP, true, bind, loseeffecttime, ItemColor);
	end
	return true;
end
--]]

function CEquipDialog:HandleDrawSprite()
	if (self.m_pEquipUISprite) then
		self.m_pEquipUISprite:RenderUISprite();
	end
end

function CEquipDialog:HandleTableDoubleClick(e)
	local MouseArgs = CEGUI.toMouseEventArgs(e);

	local pCell = CEGUI.toItemCell(MouseArgs.window);
	if (pCell == nil) then
		return false;
	end

	local roleItemManager = require("logic.item.roleitemmanager").getInstance()
    local pItem = roleItemManager:getItem(pCell:getID2(), fire.pb.item.BagTypes.EQUIP)
	if (pItem == nil) then
		return false;
	end

	self:Unequip(pItem);

	local dlg = require 'logic.tips.commontipdlg'.getInstanceNotCreate();
	if dlg then
		dlg:DestroyDialog();
	end

	return true;
end

function CEquipDialog:Unequip(item)
	local desPos = CMainPackDlg:GetSingleton():GetFirstEmptyCell();
	if (desPos == -1) then
		if (GetChatManager()) then
			GetChatManager():AddTipsMsg(120059);
		end
	else
		if (item:GetObject() ~= nil) then
			local roleItemManager = require("logic.item.roleitemmanager").getInstance()
            roleItemManager:UnEquipItem(item:GetThisID(), desPos);
		end
	end
end

function CEquipDialog:UpdateTotalScore()
	local roleScore = gGetDataManager():GetMainCharacterData().roleScore;
	local stream = "";
	stream = MHSD_UTILS.get_resstring(1637) .. roleScore;
	self.m_TotalScore:setText(stream, 0xFF3C3518);
end

function CEquipDialog:UpdateEquipTotalScore()
	local score = 0;
	local jewelryScore = 0;
	for i = 0, EQUIPNUM - 1 do
		local pCell = self.m_pEquipCell[i];
		if (pCell ~= nil) then
	        local roleItemManager = require("logic.item.roleitemmanager").getInstance()
            local pItem = roleItemManager:getItem(pCell:getID2(), fire.pb.item.BagTypes.EQUIP)
			if (pItem) then
				if (pItem:GetObject() and pItem:GetObject().bNeedRequireData) then
		            require "protodef.fire.pb.item.cgetitemtips"
		            local send = CGetItemTips.Create()
					send.packid = fire.pb.item.BagTypes.EQUIP
					send.keyinpack = pItem:GetThisID()
					LuaProtocolManager.getInstance():send(send)
				end
				if (pItem:GetSecondType() == 6) then
					jewelryScore = jewelryScore + GetEquipScore(pItem:GetLocation().tableType, pItem:GetThisID()) -- pItem:GetEquipScore();
				else
					score = score + GetEquipScore(pItem:GetLocation().tableType, pItem:GetThisID()) -- pItem:GetEquipScore();
				end
			end
		end
	end
	local stream = "";
	stream = stream .. MHSD_UTILS.get_resstring(1637);
	stream = stream .. score;
	self.m_TotalScore:setText(stream);
	stream = "";
	stream = stream .. MHSD_UTILS.get_resstring(3000);
	stream = stream .. jewelryscore;
	self.m_TotalJewelryScore:setText(stream, 0xFF3C3518);

	local total = jewelryScore + score;
	stream = "";
	stream = MHSD_UTILS.get_resstring(1637) .. total;
	self.m_TotalScore:setText(stream, 0xFF3C3518);
end

function CEquipDialog:GetItemCellByPos(pos)
	--if (pos == eEquipType_VEIL) then
	--	return self.m_pEquipCell[eEquipType_EYEPATCH];
	--end

	return self.m_pEquipCell[pos];
end

function CEquipDialog:addEquipEffect(effectId)
	self.m_pPackEquipEffect = self.m_pEquipUISprite:SetEngineSpriteDurativeEffect(MHSD_UTILS.get_effectpath(effectId), false);
    self.m_pPackEquipEffect:SetScale(1,1)
end

function CEquipDialog:removeEquipEffect()
	if (self.m_pEquipUISprite) then
		if (self.m_pPackEquipEffect) then
			self.m_pEquipUISprite:RemoveEngineSpriteDurativeEffect(self.m_pPackEquipEffect);
			self.m_pPackEquipEffect = nil;
		end
	end
end

function CEquipDialog:InitEquipEffect()
	if (self.m_pEquipUISprite) then
		local roleItemManager = require("logic.item.roleitemmanager").getInstance()
        local effectId = roleItemManager:getEquipEffectId();
		if (self.m_pPackEquipEffect == nil) then
			self.m_pPackEquipEffect = self.m_pEquipUISprite:SetEngineSpriteDurativeEffect(MHSD_UTILS.get_effectpath(effectId), false);
            if self.m_pPackEquipEffect then
                self.m_pPackEquipEffect:SetScale(1,1)
            end
		end
	end
end

function CEquipDialog:InitSpriteModel()
	local shapeid = gGetDataManager():GetMainCharacterShape();
    shapeid = shapeid
	if (self.m_pEquipUISprite) then
		if (self.m_pEquipUISprite:GetModelID() ~= shapeid) then
			self.m_pEquipUISprite:SetModel(shapeid);
		end
	else
		self.m_pEquipUISprite = UISprite:new(shapeid);
	end

	local pt = self.m_pSpriteBack:GetScreenPosOfCenter();
	local wndHeight = self.m_pSpriteBack:getPixelSize().height;
	local xPos =(pt.x);
	local yPos =(pt.y + wndHeight / 3.0);
	self.m_pEquipUISprite:SetUILocation(Nuclear.NuclearPoint(xPos, yPos));
	--self.m_pEquipUISprite:SetUIScale(1.5);

	self:UpdataModel();
end

function CEquipDialog:SetFootprint(id)
	if (id == self.m_footprint) then
		return;
	end
	self.m_footprint = id;
	if (self.m_pFootprintEffect) then
		gGetGameUIManager():RemoveUIEffect(self.m_pFootprintEffect);
		self.m_pFootprintEffect = nil;
	end
end

function CEquipDialog:OnCreate()
	self.mEventMapChangeFunctor = gGetScene().EventMapChange:InsertScriptFunctor( function()
		self:OnMapChange();
	end );

	local winMgr = CEGUI.WindowManager:getSingleton();
   -- self.jinmaiBth = CEGUI.toPushButton(winMgr:getWindow("EquipDialog/Back/jinmai"));
	self.m_pSpriteBack = CEGUI.toWindow(winMgr:getWindow("EquipDialog/spriteBack"));
	self.m_pEquipWindowBack = CEGUI.toWindow(winMgr:getWindow("EquipDialog/Back/Pattern"));
	self.m_pEquipWindowBack:setMousePassThroughEnabled(true);
	self.m_pSpriteBack:setAlwaysOnTop(true);
	--self.jinmaiBth:subscribeEvent(CEGUI.PushButton.EventClicked, CEquipDialog.HandleJinmaiBtnClick, self);
	self.jinmaipanel = CEGUI.toWindow(winMgr:getWindow("EquipDialog/Back/jinmailist/icon"));
	self.yuanshenicon = CEGUI.toWindow(winMgr:getWindow("EquipDialog/Back/jinmailist/yuanshenicon"));
	self.renwuicon = CEGUI.toWindow(winMgr:getWindow("EquipDialog/Back/jinmailist/renwuicon"));
	self.m_TotalScore = CEGUI.toWindow(winMgr:getWindow("EquipDialog/point"));
	self.scoerpanel = CEGUI.toWindow(winMgr:getWindow("EquipDialog/Back/Pattern/diban"));
	
	self.m_TotalJewelryScore = CEGUI.toWindow(winMgr:getWindow("EquipDialog/ring"));

	self.m_pEquipCell[eEquipType_CUFF] = nil --CEGUI.toItemCell(winMgr:getWindow("EquipDialog/cuff"));
	self.m_pEquipCell[eEquipType_ADORN] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/adorn"));
	self.m_pEquipCell[eEquipType_ADORNYUANSHEN] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/adornyuanshen"));
	self.m_pEquipCell[eEquipType_LORICAE] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/loricae"));
	self.m_pEquipCell[eEquipType_LORICAEYUANSHEN] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/loricaeyuanshen"));
	self.m_pEquipCell[eEquipType_ARMS] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/arms"));
	self.m_pEquipCell[eEquipType_ARMSYUANSHEN] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/armsyuanshen"));
	self.m_pEquipCell[eEquipType_TIRE] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/tire"));
	self.m_pEquipCell[eEquipType_TIREYUANSHEN] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/tireyuanshen"));
	self.m_pEquipCell[eEquipType_CLOAK] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/cloak"));
	self.m_pEquipCell[eEquipType_BOOT] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/boot"));
	self.m_pEquipCell[eEquipType_BOOTYUANSHEN] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/bootyuanshen"));
	self.m_pEquipCell[eEquipType_WAISTBAND] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/waistband"));
	self.m_pEquipCell[eEquipType_WAISTBANDYUANSHEN] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/waistbandyuanshen"));
	self.m_pEquipCell[eEquipType_EYEPATCH] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/extern01"));
	self.m_pEquipCell[eEquipType_RESPIRATOR] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/extern02"));
	self.m_pEquipCell[eEquipType_VEIL] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/extern03"));
	self.m_pEquipCell[eEquipType_FASHION] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/extern04"));
	self.m_pEquipCell[eEquipType_ERSHI] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/extern05"));
	self.m_pEquipCell[eEquipType_CHIBANG] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/extern06"));
	self.m_pEquipCell[eEquipType_FABAOA] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/extern07"));
	self.m_pEquipCell[eEquipType_FABAOB] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/extern08"));
	self.m_pEquipCell[eEquipType_JINYI] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/extern09"));
	self.m_pEquipCell[eEquipType_JINMAI1] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/extern10"));
	self.m_pEquipCell[eEquipType_JINMAI2] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/extern11"));
	self.m_pEquipCell[eEquipType_JINMAI3] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/extern12"));
	self.m_pEquipCell[eEquipType_JINMAI4] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/extern13"));
	self.m_pEquipCell[eEquipType_JINMAI5] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/extern14"));
	self.m_pEquipCell[eEquipType_JINMAI6] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/extern15"));
	self.m_pEquipCell[eEquipType_JINMAI7] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/extern16"));
	self.m_pEquipCell[eEquipType_JINMAI8] = CEGUI.toItemCell(winMgr:getWindow("EquipDialog/extern17"));

	self.m_ItemTable = GameItemTable:new(fire.pb.item.BagTypes.EQUIP);

	for index = 0, EQUIPNUM - 1 do
		if (self.m_pEquipCell[index]) then
			self.m_pEquipCell[index]:SetIndex(index);
			self.m_pEquipCell[index]:SetHaveSelectedState(true);
			self.m_pEquipCell[index]:SetCellTypeMask(1);
			self.m_pEquipCell[index]:subscribeEvent(CEGUI.ItemCell.EventCellClick, GameItemTable.HandleShowToolTips, self.m_ItemTable);
			self.m_pEquipCell[index]:subscribeEvent(CEGUI.ItemCell.EventCellClick, CMainPackDlg.HandleShowSelect, self);
			self.m_pEquipCell[index]:subscribeEvent(CEGUI.ItemCell.EventCellDoubleClick, CEquipDialog.HandleTableDoubleClick, self);
			self.m_pEquipCell[index]:subscribeEvent(CEGUI.ItemCell.EventCellDoubleClick, CMainPackDlg.HandleShowSelect, CMainPackDlg:getInstanceOrNot());
		end
	end

	--self.m_pEquipCell[eEquipType_CUFF]:SetBackGroundImage("common_pack", "kuang95");
	self.m_pEquipCell[eEquipType_ADORN]:SetBackGroundImage("common_pack", "kuang95");
	self.m_pEquipCell[eEquipType_ADORN]:SetImage("renwuui", "shipin");
	self.m_pEquipCell[eEquipType_ADORNYUANSHEN]:SetBackGroundImage("common_pack", "kuang95-");--纹饰底图
	self.m_pEquipCell[eEquipType_ADORNYUANSHEN]:SetImage("chongwuui2", "sucai18-");
	self.m_pEquipCell[eEquipType_LORICAE]:SetBackGroundImage("common_pack", "kuang95");
	self.m_pEquipCell[eEquipType_LORICAE]:SetImage("renwuui", "yifu");
	self.m_pEquipCell[eEquipType_LORICAEYUANSHEN]:SetBackGroundImage("common_pack", "kuang95-");--纹饰底图
	self.m_pEquipCell[eEquipType_LORICAEYUANSHEN]:SetImage("chongwuui2", "sucai18-");
	self.m_pEquipCell[eEquipType_ARMS]:SetBackGroundImage("common_pack", "kuang95");
	self.m_pEquipCell[eEquipType_ARMS]:SetImage("renwuui", "wuqi");
	self.m_pEquipCell[eEquipType_ARMSYUANSHEN]:SetBackGroundImage("common_pack", "kuang95-");--纹饰底图
	self.m_pEquipCell[eEquipType_ARMSYUANSHEN]:SetImage("chongwuui2", "sucai18-");
	self.m_pEquipCell[eEquipType_TIRE]:SetBackGroundImage("common_pack", "kuang95");
	self.m_pEquipCell[eEquipType_TIRE]:SetImage("renwuui", "toubu");
	self.m_pEquipCell[eEquipType_TIREYUANSHEN]:SetBackGroundImage("common_pack", "kuang95-");--纹饰底图
	self.m_pEquipCell[eEquipType_TIREYUANSHEN]:SetImage("chongwuui2", "sucai1-8");
	self.m_pEquipCell[eEquipType_CLOAK]:SetBackGroundImage("common_pack", "kuang95"); --主界面右边 灵饰戒指
	self.m_pEquipCell[eEquipType_CLOAK]:SetImage("renwuui", "shipin--");
	self.m_pEquipCell[eEquipType_BOOT]:SetBackGroundImage("common_pack", "kuang95");
	self.m_pEquipCell[eEquipType_BOOT]:SetImage("renwuui", "jiao");
	self.m_pEquipCell[eEquipType_BOOTYUANSHEN]:SetBackGroundImage("common_pack", "kuang95");--纹饰底图
	self.m_pEquipCell[eEquipType_BOOTYUANSHEN]:SetImage("chongwuui2", "sucai18-");
	self.m_pEquipCell[eEquipType_WAISTBAND]:SetBackGroundImage("common_pack", "kuang95");
	self.m_pEquipCell[eEquipType_WAISTBAND]:SetImage("renwuui", "yaodai");
	self.m_pEquipCell[eEquipType_WAISTBANDYUANSHEN]:SetBackGroundImage("common_pack", "kuang95-");--纹饰底图
	self.m_pEquipCell[eEquipType_WAISTBANDYUANSHEN]:SetImage("chongwuui2", "sucai18-");
	self.m_pEquipCell[eEquipType_EYEPATCH]:SetBackGroundImage("common_pack", "kuang95"); --下面灵饰 灵饰耳饰
	self.m_pEquipCell[eEquipType_EYEPATCH]:SetImage("renwuui", "shipin--");
	self.m_pEquipCell[eEquipType_RESPIRATOR]:SetBackGroundImage("common_pack", "kuang95"); --右下法宝 风袋
	self.m_pEquipCell[eEquipType_RESPIRATOR]:SetImage("renwuui", "shipin-");
	self.m_pEquipCell[eEquipType_VEIL]:SetBackGroundImage("common_pack", "kuang95"); --左下法宝 盘龙壁
	self.m_pEquipCell[eEquipType_VEIL]:SetImage("renwuui", "shipin--");
	self.m_pEquipCell[eEquipType_FASHION]:SetBackGroundImage("common_pack", "kuang95"); --右法宝 金甲
	self.m_pEquipCell[eEquipType_FASHION]:SetImage("renwuui", "shipin--");
	self.m_pEquipCell[eEquipType_ERSHI]:SetBackGroundImage("common_pack", "kuang95"); --左法宝 飞剑
	self.m_pEquipCell[eEquipType_ERSHI]:SetImage("renwuui", "shipin--");
	self.m_pEquipCell[eEquipType_CHIBANG]:SetBackGroundImage("common_pack", "kuang95"); --上面器灵 灵饰手镯
	self.m_pEquipCell[eEquipType_CHIBANG]:SetImage("renwuui", "shipin-");
	self.m_pEquipCell[eEquipType_FABAOA]:SetBackGroundImage("common_pack", "kuang95");--门派法宝 灵饰佩饰
	self.m_pEquipCell[eEquipType_FABAOA]:SetImage("renwuui", "shipin-");  
	self.m_pEquipCell[eEquipType_FABAOB]:SetBackGroundImage("common_pack", "kuang95");--主界面左边婚戒 
	self.m_pEquipCell[eEquipType_FABAOB]:SetImage("renwuui", "shipin-");  
	self.m_pEquipCell[eEquipType_JINYI]:SetBackGroundImage("renwuui", "xuangoukuang");--纹饰底图
	self.m_pEquipCell[eEquipType_JINYI]:SetImage("chongwuui2", "sucai18");
	self.m_pEquipCell[eEquipType_JINMAI1]:SetBackGroundImage("chongwuui2", "sucai58");--经脉开始
	self.m_pEquipCell[eEquipType_JINMAI1]:SetImage("renwuui", "xuangoukuang-");
	self.m_pEquipCell[eEquipType_JINMAI2]:SetBackGroundImage("chongwuui2", "sucai58");
	self.m_pEquipCell[eEquipType_JINMAI2]:SetImage("renwuui", "xuangoukuang-");
	self.m_pEquipCell[eEquipType_JINMAI3]:SetBackGroundImage("chongwuui2", "sucai58");
	self.m_pEquipCell[eEquipType_JINMAI3]:SetImage("renwuui", "xuangoukuang-");
	self.m_pEquipCell[eEquipType_JINMAI4]:SetBackGroundImage("chongwuui2", "sucai58");
	self.m_pEquipCell[eEquipType_JINMAI4]:SetImage("renwuui", "xuangoukuang-");
	self.m_pEquipCell[eEquipType_JINMAI5]:SetBackGroundImage("chongwuui2", "sucai58");
	self.m_pEquipCell[eEquipType_JINMAI5]:SetImage("renwuui", "xuangoukuang-");
	self.m_pEquipCell[eEquipType_JINMAI6]:SetBackGroundImage("chongwuui2", "sucai58");
	self.m_pEquipCell[eEquipType_JINMAI6]:SetImage("renwuui", "xuangoukuang-");
	self.m_pEquipCell[eEquipType_JINMAI7]:SetBackGroundImage("chongwuui2", "sucai58");
	self.m_pEquipCell[eEquipType_JINMAI7]:SetImage("renwuui", "xuangoukuang-");
	self.m_pEquipCell[eEquipType_JINMAI8]:SetBackGroundImage("chongwuui2", "sucai58");--经脉结束
	self.m_pEquipCell[eEquipType_JINMAI8]:SetImage("renwuui", "xuangoukuang-");

	self.m_pEquipStarEffect = CEGUI.toGUISheet(winMgr:getWindow("EquipDialog/Back/SpriteEffectTop"));
	self.m_pEquipStarEffect:setMousePassThroughEnabled(true);
	local pEffect = GameUImanager:createXPRenderEffect(0, function(id)
		local pMainPackDlg = CMainPackDlg:getInstanceOrNot();
		if (pMainPackDlg) then
			pMainPackDlg:HandleDrawSprite();
		end
	end );
	self.m_pSpriteBack:getGeometryBuffer():setRenderEffect(pEffect);
	gGetGameUIManager():AddUIEffect(CEGUI.toWindow(winMgr:getWindow("EquipDialog/point1")), MHSD_UTILS.get_effectpath(10242), true);

	local roleItemManager = require("logic.item.roleitemmanager").getInstance()
    roleItemManager:InitBagItem(fire.pb.item.BagTypes.EQUIP);

	self:InitSpriteModel();
	self:UpdataModel();

	self:UpdateTotalScore();

	self:InitEquipEffect();

	local roleItemManager = require("logic.item.roleitemmanager").getInstance()
    local pItem = roleItemManager:getItem(self.m_pEquipCell[eEquipType_EYEPATCH]:getID2(), fire.pb.item.BagTypes.EQUIP)
	--if pItem then
	--	if (pItem:GetSecondType() == eEquipType_VEIL) then
	--		self.m_pEquipCell[eEquipType_RESPIRATOR]:SetBackGroundImage("common_pack", "kuang94");
	--	end
	--end
end
function CEquipDialog:setShiZhuangOpenStatus(isVisible)-----yuanshen anniu 
	if isVisible ~= nil then
		self.isOpen  = isVisible
		self.isOpen1  = true
	else
		if self.isOpen == nil then 
			self.isOpen = true
			self.isOpen1  = false
		elseif self.isOpen  == false then
			self.isOpen = true
			self.isOpen1  = false
		elseif self.isOpen  == true then
			self.isOpen = false
			self.isOpen1  = true
		end
	end
		print("jingmai",self.isOpen)
	self.yuanshenicon:setVisible(false)
	
	if self.isOpen == true then
		if self.m_pPackEquipEffect or self.m_pEquipUISprite or self.m_pFootprintEffect then
			self:DestroyDialog()
			self.m_pEquipWindowBack:setVisible(false);
		--	self.jinmaiBth:setText(tostring("主角"))
			self.jinmaipanel:setVisible(true)
			self.scoerpanel:setVisible(false)
		end
	else
		self:InitSpriteModel()
		self:InitEquipEffect();
		self.m_pEquipWindowBack:setVisible(true);
	--	self.jinmaiBth:setText(tostring("经脉"))
		self.jinmaipanel:setVisible(false)
		self.scoerpanel:setVisible(true)
	end
	
	self.scoerpanel:setVisible(self.isOpen1)
	self.jinmaipanel:setVisible(false)
	self.renwuicon:setVisible(self.isOpen1)
	self.m_pEquipCell[eEquipType_ADORN]:setVisible(self.isOpen1)
	self.m_pEquipCell[eEquipType_LORICAE]:setVisible(self.isOpen1)
	self.yuanshenicon:setVisible(self.isOpen)
	self.m_pEquipCell[eEquipType_ARMS]:setVisible(self.isOpen1)
	self.m_pEquipCell[eEquipType_TIRE]:setVisible(self.isOpen1)
	self.m_pEquipCell[eEquipType_BOOT]:setVisible(self.isOpen1)
	self.m_pEquipCell[eEquipType_WAISTBAND]:setVisible(self.isOpen1)
	self.m_pEquipCell[eEquipType_EYEPATCH]:setVisible(self.isOpen)
	self.m_pEquipCell[eEquipType_FABAOB]:setVisible(self.isOpen1)
	self.m_pEquipCell[eEquipType_FABAOA]:setVisible(self.isOpen)
	self.m_pEquipCell[eEquipType_CHIBANG]:setVisible(self.isOpen)
	self.m_pEquipCell[eEquipType_CLOAK]:setVisible(self.isOpen1)
	self.m_pEquipCell[eEquipType_ERSHI]:setVisible(self.isOpen)
	self.m_pEquipCell[eEquipType_FASHION]:setVisible(self.isOpen)
	self.m_pEquipCell[eEquipType_RESPIRATOR]:setVisible(self.isOpen)
	self.m_pEquipCell[eEquipType_VEIL]:setVisible(self.isOpen)
	self.m_pSpriteBack:setVisible(self.isOpen1)
	self.m_pEquipCell[eEquipType_JINMAI1]:setVisible(false)
	self.m_pEquipCell[eEquipType_JINMAI2]:setVisible(false)
	self.m_pEquipCell[eEquipType_JINMAI3]:setVisible(false)
	self.m_pEquipCell[eEquipType_JINMAI4]:setVisible(false)
	self.m_pEquipCell[eEquipType_JINMAI5]:setVisible(false)
	self.m_pEquipCell[eEquipType_JINMAI6]:setVisible(false)
	self.m_pEquipCell[eEquipType_JINMAI7]:setVisible(false)
	self.m_pEquipCell[eEquipType_JINMAI8]:setVisible(false)
	self.m_pEquipCell[eEquipType_EYEPATCH]:setVisible(self.isOpen)
	self.m_pEquipCell[eEquipType_RESPIRATOR]:setVisible(self.isOpen)
	self.m_pEquipCell[eEquipType_ARMSYUANSHEN]:setVisible(false)
	self.m_pEquipCell[eEquipType_TIREYUANSHEN]:setVisible(false)
	self.m_pEquipCell[eEquipType_BOOTYUANSHEN]:setVisible(false)
	self.m_pEquipCell[eEquipType_WAISTBANDYUANSHEN]:setVisible(false)
end


function CEquipDialog:setjinmaiOpenStatus(isVisible)
	if isVisible ~= nil then
		self.isOpen  = isVisible
		self.isOpen1  = true
	else
		if self.isOpen == nil then 
			self.isOpen = true
			self.isOpen1  = false
		elseif self.isOpen  == false then
			self.isOpen = true
			self.isOpen1  = false
		elseif self.isOpen  == true then
			self.isOpen = false
			self.isOpen1  = true
		end
	end
	print("jingmai",self.isOpen)
	self.yuanshenicon:setVisible(false)
	
	if self.isOpen == true then
		if self.m_pPackEquipEffect or self.m_pEquipUISprite or self.m_pFootprintEffect then
			self:DestroyDialog()
			self.m_pEquipWindowBack:setVisible(false);
		--	self.jinmaiBth:setText(tostring("主角"))
			self.jinmaipanel:setVisible(true)
			self.scoerpanel:setVisible(false)
		end
	else
		self:InitSpriteModel()
		self:InitEquipEffect();
		self.m_pEquipWindowBack:setVisible(true);
	--	self.jinmaiBth:setText(tostring("经脉"))
		self.jinmaipanel:setVisible(false)
		self.scoerpanel:setVisible(true)
	end
	self.renwuicon:setVisible(self.isOpen1)
	self.m_pEquipCell[eEquipType_ADORN]:setVisible(self.isOpen1)
	self.m_pEquipCell[eEquipType_LORICAE]:setVisible(self.isOpen1)
	self.m_pEquipCell[eEquipType_ARMS]:setVisible(self.isOpen1)
	self.m_pEquipCell[eEquipType_TIRE]:setVisible(self.isOpen1)
	self.m_pEquipCell[eEquipType_BOOT]:setVisible(self.isOpen1)
	self.m_pEquipCell[eEquipType_WAISTBAND]:setVisible(self.isOpen1)
	self.m_pEquipCell[eEquipType_EYEPATCH]:setVisible(false)
	self.m_pEquipCell[eEquipType_FABAOB]:setVisible(self.isOpen1)
	self.m_pEquipCell[eEquipType_FABAOA]:setVisible(false)
	self.m_pEquipCell[eEquipType_CHIBANG]:setVisible(false)
	self.m_pEquipCell[eEquipType_CLOAK]:setVisible(self.isOpen1)
	self.m_pEquipCell[eEquipType_ERSHI]:setVisible(false)
	self.m_pEquipCell[eEquipType_FASHION]:setVisible(false)
	self.m_pEquipCell[eEquipType_RESPIRATOR]:setVisible(false)
	self.m_pEquipCell[eEquipType_VEIL]:setVisible(false)
	self.m_pEquipCell[eEquipType_JINMAI1]:setVisible(self.isOpen)
	self.m_pEquipCell[eEquipType_JINMAI2]:setVisible(self.isOpen)
	self.m_pEquipCell[eEquipType_JINMAI3]:setVisible(self.isOpen)
	self.m_pEquipCell[eEquipType_JINMAI4]:setVisible(self.isOpen)
	self.m_pEquipCell[eEquipType_JINMAI5]:setVisible(self.isOpen)
	self.m_pEquipCell[eEquipType_JINMAI6]:setVisible(self.isOpen)
	self.m_pEquipCell[eEquipType_JINMAI7]:setVisible(self.isOpen)
	self.m_pEquipCell[eEquipType_JINMAI8]:setVisible(self.isOpen)
	self.m_pEquipCell[eEquipType_ADORNYUANSHEN]:setVisible(self.isOpen)
	self.m_pEquipCell[eEquipType_RESPIRATOR]:setVisible(false)
	self.m_pEquipCell[eEquipType_ARMSYUANSHEN]:setVisible(self.isOpen)
	self.m_pEquipCell[eEquipType_TIREYUANSHEN]:setVisible(self.isOpen)
	self.m_pEquipCell[eEquipType_BOOTYUANSHEN]:setVisible(self.isOpen)
	self.m_pEquipCell[eEquipType_WAISTBANDYUANSHEN]:setVisible(self.isOpen)
end
function CEquipDialog:OnMapChange()
end


function CEquipDialog:HandleJinmaiBtnClick()
	if self.m_pPackEquipEffect or self.m_pEquipUISprite or self.m_pFootprintEffect then
		self:DestroyDialog()
		self.m_pEquipWindowBack:setVisible(false);
		self.jinmaiBth:setText(tostring("主角"))
		self.jinmaipanel:setVisible(true)
		self.scoerpanel:setVisible(false)
	else
		self:InitSpriteModel()
		self:InitEquipEffect();
		self.m_pEquipWindowBack:setVisible(true);
		self.jinmaiBth:setText(tostring("经脉"))
		self.jinmaipanel:setVisible(false)
		self.scoerpanel:setVisible(true)
	end
end


function CEquipDialog:DestroyDialog()
	if (self.m_pPackEquipEffect) then
		self.m_pEquipUISprite:RemoveEngineSpriteDurativeEffect(self.m_pPackEquipEffect);
		self.m_pPackEquipEffect = nil;
	end
	if (self.m_pEquipUISprite) then
		self.m_pEquipUISprite:delete();
		self.m_pEquipUISprite = nil;
	end
	if (self.m_pFootprintEffect) then
		gGetGameUIManager():RemoveUIEffect(m_pFootprintEffect);
		m_pFootprintEffect = nil;
	end
	self:removeEventMapChangeFunctor();
end

function CEquipDialog:delete()
	self:removeEventMapChangeFunctor();
end

function CEquipDialog.new(parent)
	local obj = { };
	setmetatable(obj, CEquipDialog);

	obj.m_pEquipUISprite = nil;
	obj.m_footprint = 0;
	obj.m_pFootprintEffect = nil;
	obj.m_pPackEquipEffect = nil;
	obj.m_pParentWindow = parent;
	obj.mEventMapChangeFunctor = nil;

	return obj;
end

return CEquipDialog;
