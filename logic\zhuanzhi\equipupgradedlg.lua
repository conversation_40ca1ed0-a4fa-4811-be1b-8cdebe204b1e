require "logic.dialog"
require "logic.zhuanzhi.zhuanzhiwuqicell"

EquipUpgradeDlg = {}
setmetatable(EquipUpgradeDlg, Dialog)
EquipUpgradeDlg.__index = EquipUpgradeDlg

local _instance
function EquipUpgradeDlg.getInstance()
	if not _instance then
		_instance = EquipUpgradeDlg:new()
		_instance:OnCreate()
	end
	return _instance
end

function EquipUpgradeDlg.getInstanceAndShow()
	if not _instance then
		_instance = EquipUpgradeDlg:new()
		_instance:OnCreate()
	else
		_instance:SetVisible(true)
	end
	return _instance
end

function EquipUpgradeDlg.getInstanceNotCreate()
	return _instance
end

function EquipUpgradeDlg.DestroyDialog()
	if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
end

function EquipUpgradeDlg.ToggleOpenClose()
	if not _instance then
		_instance = EquipUpgradeDlg:new()
		_instance:OnCreate()
	else
		if _instance:IsVisible() then
			_instance:SetVisible(false)
		else
			_instance:SetVisible(true)
		end
	end
end

function EquipUpgradeDlg.GetLayoutFileName()
	return "equipupgrade.layout"
end

function EquipUpgradeDlg:new()
	local self = {}
	self = Dialog:new()
	setmetatable(self, EquipUpgradeDlg)
	return self
end

function EquipUpgradeDlg:OnCreate()
	Dialog.OnCreate(self)
	local winMgr = CEGUI.WindowManager:getSingleton()
	local roleItemManager = require("logic.item.roleitemmanager").getInstance()

	self.m_WuqiList = winMgr:getWindow("advancedequip/left")

	self.m_CurWuqi = CEGUI.toItemCell(winMgr:getWindow("advancedequip/right/part2/line1/tb/item"))

	self.m_NextWuqi = CEGUI.toItemCell(winMgr:getWindow("advancedequip/right/part2/item2"))
	self.m_NextWuqiText = winMgr:getWindow("advancedequip/right/part2/name2")
	self.m_NextWuqiText:setText("")

	self.m_chongzhushi = winMgr:getWindow("zhuanzhiwuqi/di2/text21")
	self.m_chongzhushi:setText("")

	self.m_wuqicount = winMgr:getWindow("zhuanzhiwuqi/di2/text211")
	self.m_wuqicount:setText("")

	self.m_CurYinBi = winMgr:getWindow("advancedequip/right/bg11/2")
	self.m_CurYinBi:setText(formatMoneyString(CurrencyManager.getOwnCurrencyMount(fire.pb.game.MoneyType.MoneyType_GoldCoin)))
	self.m_NeedYinBi = winMgr:getWindow("advancedequip/right/bg1/one")
	self.m_NeedYinBi:setText(formatMoneyString(0))

	self.m_TransfromBtn = CEGUI.toPushButton(winMgr:getWindow("advancedequip/right/button"))
	self.m_TransfromBtn:subscribeEvent("MouseButtonUp", EquipUpgradeDlg.HandlerTransfromBtn, self)

	self.m_WeaponInfo = CEGUI.toRichEditbox(winMgr:getWindow("advancedequip/right/shuxinglist/box"))
	self.nItemCellSelId = 0

	self.m_CurWeaponID = -1
	self.m_NextWeaponID = -1
	self:RefreshAllWeaponData()
end

function EquipUpgradeDlg:RefreshAllWeaponData()

	self.m_CurWuqi:SetImage(nil)
	SetItemCellBoundColorByQulityItem(self.m_CurWuqi, 0)

	self.m_NextWuqi:SetImage(nil)
	self.m_NextWuqiText:setText("")
	SetItemCellBoundColorByQulityItem(self.m_NextWuqi, 0)

	self.m_CurWeaponID = -1
	self.m_NextWeaponID = -1

	self.m_WeaponInfo:Clear()
	self.m_WeaponInfo:Refresh()

	local roleItemManager = require("logic.item.roleitemmanager").getInstance()
	self.m_CurYinBi:setText(formatMoneyString(CurrencyManager.getOwnCurrencyMount(fire.pb.game.MoneyType.MoneyType_GoldCoin)))

	self:initWeaponList()
end

function EquipUpgradeDlg:initWeaponList()
	self.vWeaponKey = { }
	local keys = {}
	local roleItemManager = require("logic.item.roleitemmanager").getInstance()
	keys = roleItemManager:GetItemKeyListByType(keys, eItemType_EQUIP, fire.pb.item.BagTypes.BAG)
	self.vTableId = { }
	self:GetTableIdArray(keys)

	if self.m_tableview then
		self.itemOffect = self.m_tableview:getContentOffset()
	end

	local len = #self.vTableId
	if not self.m_tableview then
		local s = self.m_WuqiList:getPixelSize()
		self.m_tableview = TableView.create(self.m_WuqiList)
		self.m_tableview:setViewSize(s.width, s.height)
		self.m_tableview:setPosition(0, 0)
		self.m_tableview:setDataSourceFunc(self, EquipUpgradeDlg.tableViewGetCellAtIndex)
	end
	self.m_tableview:setCellCountAndSize(len, 370, 113)
	self.m_tableview:setContentOffset(self.itemOffect or 0)
	self.m_tableview:reloadData()

end

function EquipUpgradeDlg:tableViewGetCellAtIndex(tableView, idx, cell)
    if not cell then
        cell = ZhuanZhiWuQiCell.CreateNewDlg(tableView.container, tableView:genCellPrefix())
        cell.btnBg:subscribeEvent("MouseClick", EquipUpgradeDlg.HandleClickedItem,self)
    end
    self:setGemCellInfo(cell, idx+1)
    return cell
end

function EquipUpgradeDlg:setGemCellInfo(cell, index)
	local nTabId = self.vTableId[index]
	local itemAttrCfg = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(nTabId)
	if not itemAttrCfg then
		return
	end

    cell.btnBg:setID(nTabId)
	cell.btnBg:setID2(self.vWeaponKey[index])
	cell.name:setText(itemAttrCfg.name)
	cell.itemCell:SetImage(gGetIconManager():GetItemIconByID(itemAttrCfg.icon))
    SetItemCellBoundColorByQulityItemWithId(cell.itemCell, itemAttrCfg.id)


	if self.nItemCellSelId ==0 then
		self.nItemCellSelId = nTabId
	end

    if self.nItemCellSelId ~= nTabId then
        cell.btnBg:setSelected(false)
    else
        cell.btnBg:setSelected(true)
    end
    
end

function EquipUpgradeDlg:GetTableIdArray(vGemKey)
	local roleItemManager = require("logic.item.roleitemmanager").getInstance()
	local needlv = tonumber(GameTable.common.GetCCommonTableInstance():getRecorder(434).value)
	for i = 0, vGemKey:size() -1 do
		local baggem = roleItemManager:FindItemByBagAndThisID(vGemKey[i], fire.pb.item.BagTypes.BAG)
		if baggem then
			local nTableId = baggem:GetObjectID()
			local itemAttrCfg = baggem:GetBaseObject()
			local nextEquipCfg = BeanConfigManager.getInstance():GetTableByName("item.cequipupgradeconfig"):getRecorder(nTableId)
			if nextEquipCfg ~= nil  then
				self.vTableId[#self.vTableId + 1] = nTableId
				self.vWeaponKey[#self.vWeaponKey + 1] = vGemKey[i]
			end
			
		end
	end
end

function EquipUpgradeDlg:HandleClickedItem(e)
	self.m_chongzhushi:setText("")
	self.m_wuqicount:setText("")
	local mouseArgs = CEGUI.toMouseEventArgs(e)
	local id = mouseArgs.window:getID()
	local key = mouseArgs.window:getID2()
	local itemAttrCfg1 = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(id)
	if not itemAttrCfg1 then
		return
	end
	local nextEquipCfg = BeanConfigManager.getInstance():GetTableByName("item.cequipupgradeconfig"):getRecorder(id)
	local needitem  = BeanConfigManager.getInstance():GetTableByName("item.cequipupgradeconfig"):getRecorder(id).needitemid
	local needitemicon = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(needitem)

	local equipConfig = BeanConfigManager.getInstance():GetTableByName("item.cequipupgradeconfig"):getRecorder(id)
	local curOwn = RoleItemManager.getInstance():GetItemNumByBaseID(equipConfig.needitemid)
	local needitemcount = BeanConfigManager.getInstance():GetTableByName("item.cequipupgradeconfig"):getRecorder(id).needitemcount
	local curOwnweapon = RoleItemManager.getInstance():GetItemNumByBaseID(equipConfig.id)
	local needweapomcount = BeanConfigManager.getInstance():GetTableByName("item.cequipupgradeconfig"):getRecorder(id).needweaponcount
	local needitemname = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(needitem).name
	self.m_chongzhushi:setText(string.format("%s(%d/%d)",needitemname,needitemcount,curOwn))
	self.m_wuqicount:setText(string.format("%s(%d/%d)","装备需求",needweapomcount,curOwnweapon))

	if nextEquipCfg == nil then
		-- 此装备不可升阶
		GetCTipsManager():AddMessageTipById(196010)
		return
	end
	self.m_NeedMoneyText = nextEquipCfg.needCold
	self.m_NeedYinBi:setText(formatMoneyString(self.m_NeedMoneyText))
	--self.m_NeedYinBi:setText(tostring(self.m_NeedMoneyText))
	self.m_CurWeaponID = id
	self.m_NextWeaponID = nextEquipCfg.newEquipID
	self.m_CurWuqi:SetImage(gGetIconManager():GetItemIconByID(needitemicon.icon))
	SetItemCellBoundColorByQulityItemWithId(self.m_CurWuqi, needitemicon.id)

	local itemAttrCfg2 = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(self.m_NextWeaponID)
	if not itemAttrCfg2 then
		return
	end

	self.m_NextWuqi:SetImage(gGetIconManager():GetItemIconByID(itemAttrCfg2.icon))
	self.m_NextWuqiText:setText(itemAttrCfg2.name)
	SetItemCellBoundColorByQulityItemWithId(self.m_NextWuqi, itemAttrCfg2.id)

	self:ShowWeaponProperty(key)

end

function EquipUpgradeDlg:ShowWeaponProperty(wid)
	self.m_WeaponInfo:Clear()
	local Itemkey = wid
	local roleItemManager = require("logic.item.roleitemmanager").getInstance()
	local pItem = roleItemManager:FindItemByBagAndThisID(Itemkey, fire.pb.item.BagTypes.BAG)
	local pObj = nil
	if pItem then
		pObj = pItem:GetObject()
		local vcBaseKey = pObj:GetBaseEffectAllKey()
		local color = CEGUI.ColourRect(CEGUI.PropertyHelper:stringToColour("ff261407"))
		local color_blue = CEGUI.ColourRect(CEGUI.PropertyHelper:stringToColour("ff00b1ff"))
		local color_red = CEGUI.ColourRect(CEGUI.PropertyHelper:stringToColour("ffff0000"))
		for nIndex = 1, #vcBaseKey do
			local nBaseId = vcBaseKey[nIndex]
			local nBaseValue = pObj:GetBaseEffect(nBaseId)

			if nBaseValue ~= 0 then
				local propertyCfg = BeanConfigManager.getInstance():GetTableByName("item.cattributedesconfig"):getRecorder(nBaseId)
				if propertyCfg and propertyCfg.id ~= -1 then
					local strTitleName = propertyCfg.name
					local nValue = math.abs(nBaseValue)
					if nBaseValue > 0 then
						strTitleName = strTitleName .. " " .. tostring(nValue) .. "+"
					elseif nBaseValue < 0 then
						strTitleName = strTitleName .. " " .. tostring(nValue) .. "-"
					end
					strTitleName = "  " .. strTitleName
					strTitleName = CEGUI.String(strTitleName)
					self.m_WeaponInfo:AppendText(strTitleName, color)
					local addValue = "[升级属性]"
					self.m_WeaponInfo:AppendText(CEGUI.String(addValue), color_red)
					self.m_WeaponInfo:AppendBreak()
				end
			end
		end

		local vPlusKey = pObj:GetPlusEffectAllKey()
		for nIndex = 1, #vPlusKey do
			local nPlusId = vPlusKey[nIndex]
			local mapPlusData = pObj:GetPlusEffect(nPlusId)
			if mapPlusData.attrvalue ~= 0 then

				local nPropertyId = mapPlusData.attrid
				local nPropertyValue = mapPlusData.attrvalue
				local propertyCfg = BeanConfigManager.getInstance():GetTableByName("item.cattributedesconfig"):getRecorder(nPropertyId)
				if propertyCfg and propertyCfg.id ~= -1 then
					local strTitleName = propertyCfg.name
					local nValue = math.abs(nPropertyValue)
					if nPropertyValue > 0 then
						strTitleName = strTitleName .. " " .. "+" .. tostring(nValue)
					else
						strTitleName = strTitleName .. " " .. "-" .. tostring(nValue)
					end
					local strEndSpace = "  "
					local strBeginSpace = "  "
					strTitleName = strTitleName .. strEndSpace
					strTitleName = strBeginSpace .. strTitleName

					strTitleName = CEGUI.String(strTitleName)
					self.m_WeaponInfo:AppendText(strTitleName, color_blue)
					self.m_WeaponInfo:AppendBreak()
				end
			end
		end
		self.m_WeaponInfo:Refresh()
	end
end

function EquipUpgradeDlg:HandlerTransfromBtn(e)
	if self.m_CurWeaponID == -1 then
		GetCTipsManager():AddMessageTipById(174023)
		return
	end

	if self.m_NextWeaponID == -1 then
		GetCTipsManager():AddMessageTipById(174023)
		return
	end
	local roleItemManager = require("logic.item.roleitemmanager").getInstance()
	if CurrencyManager.getOwnCurrencyMount(fire.pb.game.MoneyType.MoneyType_GoldCoin) < self.m_NeedMoneyText then
		GetCTipsManager():AddMessageTipById(196010)
		return
	end


	local needitem  = BeanConfigManager.getInstance():GetTableByName("item.cequipupgradeconfig"):getRecorder(self.m_CurWeaponID).needitemid
	local needitemcount = BeanConfigManager.getInstance():GetTableByName("item.cequipupgradeconfig"):getRecorder(self.m_CurWeaponID).needitemcount
	local shengjishiCount = RoleItemManager.getInstance():GetItemNumByBaseID(needitem)
	local weaponCount = RoleItemManager.getInstance():GetItemNumByBaseID(self.m_CurWeaponID)
	local needweaponcount = BeanConfigManager.getInstance():GetTableByName("item.cequipupgradeconfig"):getRecorder(self.m_CurWeaponID).needweaponcount
	if weaponCount < needweaponcount then
		GetCTipsManager():AddMessageTipById(196048) --这里你自己加一个客户端提示，武器需求数量不足
		return
	end
	if shengjishiCount < needitemcount then 
		GetCTipsManager():AddMessageTipById(196032)
		return
	end
	local dlg = require "logic.zhuanzhi.equipupjinjiegradeconfirmdlg".getInstanceAndShow()
	if dlg then
		local itemAttrCfg1 = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(self.m_CurWeaponID)
		if not itemAttrCfg1 then
			return
		end

		local itemAttrCfg2 = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(self.m_NextWeaponID)
		if not itemAttrCfg2 then
			return
		end
		
		local Itemkey = 0
		for i = 1, #self.vTableId do
			if self.vTableId[i] == self.m_CurWeaponID then
				Itemkey = self.vWeaponKey[i]
				break
			end
		end

		dlg:SetInfoData(itemAttrCfg1.name, itemAttrCfg2.name, self.m_NeedMoneyText, Itemkey, self.m_NextWeaponID)
	end
end

return EquipUpgradeDlg