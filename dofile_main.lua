--[[
================================================================================
大唐游戏客户端模块加载入口文件
================================================================================
文件名: dofile_main.lua
功能: 负责加载所有游戏模块，注册Lua函数到C++，初始化游戏系统
作者: 大唐游戏开发团队 (调试部分 by liugeng)
创建时间: 未知
最后修改: 未知
================================================================================
]]--

-- ============================================================================
-- 调试模式设置
-- ============================================================================
-- 仅用于调试，作者：liugeng
if DeviceInfo:sGetDeviceType()==4 then --WIN7_32 Windows 32位系统
    require "debughelper"  -- 加载调试助手
else
    _G.debugrequire=_G.require  -- 其他平台使用标准require
end

-- ============================================================================
-- 协议定义模块
-- ============================================================================
-- 加载协议定义，用于Lua函数注册到C++
require "protodef.protocols"

-- ============================================================================
-- 核心管理器模块
-- ============================================================================
require "logic.showhide"                    -- 显示/隐藏管理
require "manager.protocolhandlermanager"    -- 协议处理管理器
require "manager.luaprotocolmanager"        -- Lua协议管理器
require "manager.beanconfigmanager"         -- Bean配置管理器
require "manager.notifymanager"             -- 通知管理器
require "manager.npcservicemanager"         -- NPC服务管理器
require "manager.currencymanager"           -- 货币管理器
require "manager.notificationcenter"        -- 通知中心
require "manager.teammanager"               -- 队伍管理器
require "manager.mt3heromanager"            -- 英雄管理器
require "manager.welfaremanager"            -- 福利管理器
require "manager.npcservicemanager"         -- NPC服务管理器(重复引用)
require "manager.taskmanager_ctolua"        -- 任务管理器(C++到Lua)
require "manager.mainroledatamanager"       -- 主角色数据管理器

-- ============================================================================
-- 游戏逻辑管理器
-- ============================================================================
require "logic.shop.shopmanager"            -- 商店管理器
require "logic.skillboxcontrol"             -- 技能栏控制
require "logic.luauimanager"                -- Lua UI管理器
require "logic.team.formationmanager"       -- 阵型管理器
require "logic.battle.battlemanager"        -- 战斗管理器
require "logic.skill.roleskillmanager"      -- 角色技能管理器

-- ============================================================================
-- 对话框和界面模块
-- ============================================================================
require "logic.contactroledlg"              -- 联系角色对话框
require "logic.rank.zonghepingfendlg"       -- 综合评分对话框

-- ============================================================================
-- 工具类模块
-- ============================================================================
require "utils.typedefine"                  -- 类型定义
require "utils.log"                         -- 日志工具
require "utils.bit"                         -- 位操作工具
require "utils.commonutil"                  -- 通用工具

-- ============================================================================
-- 宠物和引导系统
-- ============================================================================
require "logic.pet.mainpetdatamanager"      -- 主宠物数据管理器
require "logic.guide.newroleguidemanager"   -- 新角色引导管理器

-- ============================================================================
-- 核心系统模块
-- ============================================================================
require "mainticker"                        -- 主循环计时器
require "globalfunctionsforcpp"             -- 全局C++接口函数
-- ============================================================================
-- UI对话框模块
-- ============================================================================
-- 注意：由于侠客相关Lua文件的嵌套关系非常复杂，暂时保留在此文件中
require "logic.texttip"                     -- 文本提示
require "logic.multimenu"                   -- 多级菜单
require "logic.numkeyboarddlg"              -- 数字键盘对话框
require "logic.tableview"                   -- 表格视图
require "logic.treeview"                    -- 树形视图
require "logic.messageboxsimple"            -- 简单消息框
require "logic.selectserversdialog"         -- 选择服务器对话框
require "logic.maincontrol"                 -- 主控制器
require "logic.workshop.workshoplabel"     -- 工坊标签
require "utils.reload_util"                 -- 重载工具
require "logic.battle.zhenfa"               -- 阵法
require "logic.battle.zhenfatip"            -- 阵法提示
require "logic.pet.petlabel"                -- 宠物标签
require "logic.checktipswnd"                -- 检查提示窗口
require "logic.loginqueuedialog"            -- 登录队列对话框
require "logic.task.taskdialog"             -- 任务对话框
require "logic.task.renwulistdialog"        -- 任务列表对话框
require "logic.luanewroleguide"             -- Lua新角色引导
require "logic.chatinsimpleshow"            -- 聊天简单显示
require "logic.chargedialog"                -- 充值对话框
require "logic.skill.skilllable"            -- 技能标签
require "logic.task.showtaskdetail"         -- 显示任务详情

-- ============================================================================
-- C++中使用的模块
-- ============================================================================
-- 已在C++中使用，需要在此处引入
require "logic.jingying.jingyingenddlg"     -- 精英结束对话框
require "logic.rank.rankinglist"            -- 排行榜列表

-- ============================================================================
-- 物品系统模块 (从C++到Lua)
-- ============================================================================
require "logic.item.itemcommon"             -- 物品通用
require "logic.item.roleitem"               -- 角色物品
require "logic.item.roleitemmanager"        -- 角色物品管理器

-- ============================================================================
-- 物品处理和战斗相关
-- ============================================================================
require "logic.useitemhandler"              -- 使用物品处理器
require "logic.pet.battlepetsummondlg"      -- 战斗宠物召唤对话框
require "logic.battle.luabattleuimanager"   -- Lua战斗UI管理器

-- 已在C++中使用，需要在此处引入
require "logic.systemsettingdlgnew"         -- 系统设置对话框(新)

-- ============================================================================
-- 战斗和角色相关对话框
-- ============================================================================
require "logic.battleautofightdlg"          -- 战斗自动战斗对话框
require "logic.pet.petoperatedlg"           -- 宠物操作对话框
require "system.banlistmanager"             -- 封禁列表管理器
require "logic.task.specialgotonpc"         -- 特殊前往NPC
require "logic.switchaccountdialog"         -- 切换账号对话框

require "logic.battle.zhandouanniu"         -- 战斗按钮
require "logic.characterinfo.characterpropertyaddptrdlg"  -- 角色属性加点对话框
require "logic.addpointintro"               -- 加点介绍
require "logic.addpointlistdlg"             -- 加点列表对话框
require "logic.team.teamdialognew"          -- 队伍对话框(新)

require "logic.characterinfo.characterpropertyaddptrresetcell"    -- 角色属性加点重置单元格
require "logic.characterinfo.characterpropertyaddptrresetdlg"     -- 角色属性加点重置对话框

require "logic.characterinfo.addpointmanger"  -- 加点管理器

-- ============================================================================
-- 技能和工坊相关
-- ============================================================================
require "logic.skill.lifeskilltipdlg"       -- 生活技能提示对话框
require "logic.workshop.tipsguajiimgcell"   -- 提示挂机图片单元格
require "logic.workshop.tipsguajiimg"       -- 提示挂机图片
require "logic.bingfengwangzuo.bingfengwangzuoTaskTips"  -- 冰封王座任务提示
require "logic.battle.battlebag"            -- 战斗背包
require "logic.battle.battletips"           -- 战斗提示
require "logic.battle.battleskilltip"       -- 战斗技能提示
require "logic.battle.battleskillpanel"     -- 战斗技能面板
require "logic.battle.characteroperatedlg"  -- 角色操作对话框

-- ============================================================================
-- 提示和地图相关
-- ============================================================================
require "logic.tips.equipinfotips"          -- 装备信息提示
require "logic.tips.petskilltipsdlg"        -- 宠物技能提示对话框
require "logic.deathNoteDlg"                -- 死亡笔记对话框
require "logic.treasureMap.treasureChosedDlg"     -- 藏宝图选择对话框
require "logic.treasureMap.supertreasuremap"      -- 超级藏宝图

require "logic.pet.petstoragedlg"           -- 宠物存储对话框

-- ============================================================================
-- 地图和重复模块 (注意：部分模块重复引入)
-- ============================================================================
require "logic.mapchose.hookmanger"         -- 地图选择挂机管理器
require "logic.showhide"                    -- 显示/隐藏 (重复)
require "logic.battleautofightdlg"          -- 战斗自动战斗对话框 (重复)
require "logic.pet.battlepetsummondlg"      -- 战斗宠物召唤对话框 (重复)
require "logic.battle.luabattleuimanager"   -- Lua战斗UI管理器 (重复)
require "logic.pet.petoperatedlg"           -- 宠物操作对话框 (重复)
require "logic.battle.userminiicondlg"      -- 用户迷你图标对话框
require "logic.battle.battlebag"            -- 战斗背包 (重复)
require "logic.battle.battletishi"          -- 战斗提示
require "logic.battle.zhandouanniu"         -- 战斗按钮 (重复)
require "logic.battle.battletips"           -- 战斗提示 (重复)
require "logic.battle.battletipscell"       -- 战斗提示单元格
require "logic.battle.bossdaxuetiao"        -- Boss大血条
require "logic.skill.skilllable"            -- 技能标签 (重复)
require "logic.skill.characterskillupdatedlg"  -- 角色技能升级对话框
require "logic.task.taskuseitemdialog"      -- 任务使用物品对话框
require "logic.task.taskhelper"             -- 任务助手

require "logic.qiandaosongli.qiandaosonglicell_mtg"
require "logic.qiandaosongli.qiandaosonglidlg_mtg"
require "logic.qiandaosongli.loginrewardmanager"
require "logic.qiandaosongli.continuedaycell"
require "logic.qiandaosongli.continuedayreward"
require "logic.qiandaosongli.mtg_firstchargedlg"
require "logic.qiandaosongli.mtg_onlinewelfaredlg"
require "logic.qiandaosongli.fengcefanhuandlg"
require "logic.team.huobanzhuzhanjiesuo"
require "logic.zhenfa.zhenfadlg"
require "logic.bingfengwangzuo.bingfengwangzuodlg"
require "logic.bingfengwangzuo.bingfengwangzuoTips"

require "logic.qiandaosongli.leveluprewarddlg"
require "logic.qiandaosongli.leveluprewardcell"
require "logic.pet.petdetaildlg"
require "logic.npcnamelist"
--�������
require "logic.family.family" --����UI
require "logic.family.familyjiarudialog" -- ���빫��UI
require "logic.family.familylabelframe"   --�����ǩҳ
require "logic.family.familychengyuandialog" --�����ԱUI
require "logic.family.familyfulidialog"	--���ḣ��UI
require "logic.family.familyhuodongdialog"    --����
require "logic.family.familychuangjiandialog" --��������
require "logic.family.familyBossBloodBar" -- ����BOSS
require "logic.family.familyfightrank" -- ����ս����
require "logic.libaoduihuan.libaoduihuanma" --����һ���
require "logic.leitai.leitaidialog"   --PK
require "logic.chakan.chakan" --�鿴����װ��
require "logic.addnewitemseffect"
require "logic.shijiebobaodlg"
require "logic.yangchengnotify.yangchenglistdlg"
require "logic.team.teamrollmelondialog"
require "logic.ransedlg"
require "logic.ransebaocundlg"
require "logic.bingfengwangzuo.bingfengwangzuomanager"
require "logic.numbersel"
require "logic.guajicfg"
require "logic.item.MainPackLabelDlg" -- ������ֿ�
require "logic.item.gameitemtable"
require "logic.readtimeprogressdlg"
require "logic.scenemovie.scriptnpctalkdialog"
require "logic.shengsizhan.shengsizhanteampanel"
require "logic.shengsizhan.shengsizhandlg"
require "logic.shengsizhan.shengsizhanguizedlg"
require "logic.shengsizhan.shengsizhanwatchdlg"
require "logic.shengsizhan.shengsibangdlg"

require "logic.exp.mainroleexpdlg"
require "logic.petandusericon.userandpeticon"

-- ��½
require "logic.login.logindlg"
require "logic.login.LoginImageAndBar"
-- ��ʱ����
require "logic.item.TemporaryPack"

--�������
require "logic.chat.cchatoutboxoperateldlg"
require "logic.chat.chatoutputdialog"
require "logic.chat.chatcommon"
require "logic.chat.chatmanager"
require "logic.chat.insertdlg"
require "logic.chat.roleaccusation"
require "logic.chat.chatcellmanager"
require "logic.chat.tipsmanager"

--תְ���
require "logic.zhuanzhi.zhuanzhidlg"
require "logic.zhuanzhi.zhuanzhibaoshi"
require "logic.zhuanzhi.zhuanzhicommon"
require "logic.zhuanzhi.zhuanzhiwuqidlg"

require "logic.fuyuanbox.fuyuanboxdlg"
require "logic.newguide.guideModelSelectDlg"

require "logic.wisdomtrialdlg.wisdomrrialhelpdlg"
require "logic.redpack.redpacklabel"
require "logic.redpack.redpackmanager"
require "logic.worldmap.worldmapdlg"
require "logic.worldmap.worldmapdlg1"

require "logic.newguide.newguidebg"
require "logic.openui"
require "logic.chat.chatmanager"

require "logic.space.spacemanager"
require "logic.json"



require "logic.pointcardserver.currencytradingdlg"
require "logic.pointcardserver.pointcardservermanager"

require "logic.GameCenterDefine"

require "logic.friend.friendmanager"

require "logic.login.loginbackground"
require "logic.windowsexplain"

require "logic.fightorder.ordereditordlg"
require "logic.fightorder.ordereditorinput"
require "logic.fightorder.ordersetdlg"

require "logic.pipeidlg"


function QuickCommandToC(Cmd, Param0, Param1, Param2, Param3)
	gGetGameUIManager():QuickCommand(Cmd, Param0, Param1, Param2, Param3)
end
function ReloadLua(FileName)
	debugrequire (FileName)
end
function QuickCommand(Cmd, Param0, Param1, Param2, Param3)
	if Cmd == "ReloadLua" then
		ReloadLua(Param0)
	end
end



--debugrequire "logic.showhide"

function Entry_Init()
	LogInfo("dofile enter init")
--	pcall(require "debug.debugger")

	if DeviceInfo:sGetDeviceType()==4 then --WIN7_32

	else
		CFileUtil:DelFileArrOfPath(CFileUtil:GetTempDir() .. "/", ".wav", false)
		CFileUtil:DelFileArrOfPath(CFileUtil:GetTempDir() .. "/", ".amr", false)
	end

	gGetGameApplication():RegisterTickerHandler(LuaMainTick)

	ProtocolHandlerManager.RegisterProtocolScriptHandler()

	--lua protocols
	Game.gGetProtocolLuaFunManager():RegisterLuaProtocolHandler(LuaProtocolManager.Dispatch)
	RegisterLuaProtocols()


	BeanConfigManager.getInstance():Initialize("/table/xmltable/", "/table/bintable/")

	--[[
	local tt = GameTable.mission.GetCMainMissionInfoTableInstance()

	t = tt:getRecorder(100101)
	for k,v in pairs(t.PostMissionList) do
		print("xml -- "..k .. v)
	end
	--]]
end

Entry_Init()

--gGetGameApplication():SetReadXmlFromBinary(false)
MT3.ChannelManager:RemoveRCPFiles(30)

function OnAuthError()
	LoginQueueDlg.DestroyDialog()
end

function OnAuthError2(detail)
	--if gGetFriendsManager() then
		--local strContent = string.format("<T t=\"%s\" c=\"FF693F00\"></T>", detail)
--		gGetFriendsManager():AddChatRecord(0, -1, "", "", strContent)
	--end
end

----------end/////////////////////////////////
