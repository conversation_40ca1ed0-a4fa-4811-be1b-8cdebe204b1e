require "logic.dialog"
require "logic.zhuanzhi.zhuanzhiwuqicell"
require "utils.temputil"

ZhuanZhiWuQiDlg = {}
ZhuanZhiType = {
    1288, --剑侠客,刀
    3080, --逍遥生,扇
    1800, --杀破狼，弓
    4104, --骨精灵,爪
    4360, --龙太子,枪
    2824, --玄彩娥,棒子
    3080, --飞燕女,环
    2824, --虎头怪,双锤
}
setmetatable(ZhuanZhiWuQiDlg, Dialog)
ZhuanZhiWuQiDlg.__index = ZhuanZhiWuQiDlg

local _instance
function ZhuanZhiWuQiDlg.getInstance()
    if not _instance then
        _instance = ZhuanZhiWuQiDlg:new()
        _instance:OnCreate()
    end
    return _instance
end

function ZhuanZhiWuQiDlg.getInstanceAndShow()
    print("------------->into ZhuanZhiWuQiDlg.getInstanceAndShow")
    if not _instance then
        _instance = ZhuanZhiWuQiDlg:new()
        _instance:OnCreate()
    else
        _instance:SetVisible(true)
    end
    return _instance
end

function ZhuanZhiWuQiDlg.getInstanceNotCreate()
    return _instance
end

function ZhuanZhiWuQiDlg.DestroyDialog()
    if _instance then
        if not _instance.m_bCloseIsHide then
            _instance:OnClose()
            _instance = nil
        else
            _instance:ToggleOpenClose()
        end
    end
end
function ZhuanZhiWuQiDlg.ToggleOpenClose()
    if not _instance then
        _instance = ZhuanZhiWuQiDlg:new()
        _instance:OnCreate()
    else
        if _instance:IsVisible() then
            _instance:SetVisible(false)
        else
            _instance:SetVisible(true)
        end
    end
end


function ZhuanZhiWuQiDlg.GetLayoutFileName()
    return "zhuanzhiwuqi.layout"
end

function ZhuanZhiWuQiDlg:new()
    local self = {}
    self = Dialog:new()
    setmetatable(self, ZhuanZhiWuQiDlg)
    return self
end

function ZhuanZhiWuQiDlg:OnCreate()
    --转换武器最低等级
    self.needlv = tonumber(GameTable.common.GetCCommonTableInstance():getRecorder(434).value)
    print("->>>>>>>>>into ZhuanZhiBaoShiConfrimDlg:OnCreate")
    Dialog.OnCreate(self)
    local winMgr = CEGUI.WindowManager:getSingleton()
    local roleItemManager = require("logic.item.roleitemmanager").getInstance()
    
    --包内武器列表
    self.m_WuqiList = winMgr:getWindow("zhuanzhiwuqi/di1/list")
    
    --当前武器
    self.m_CurWuqi = CEGUI.toItemCell(winMgr:getWindow("zhuanzhiwuqi/di2/wuqi1"))
    self.m_CurWuqiText = winMgr:getWindow("zhuanzhiwuqi/di2/text1")
    self.m_CurWuqiText:setText("")

    --目标武器
    self.m_NextWuqi = CEGUI.toItemCell(winMgr:getWindow("zhuanzhiwuqi/di2/wuqi2"))
    self.m_NextWuqiText = winMgr:getWindow("zhuanzhiwuqi/di2/text2")
    self.m_NextWuqiText:setText("")

    --包里有多少转换道具
    self.m_CurYinBi = winMgr:getWindow("zhuanzhiwuqi/kuang1/di1/text1")
    --self.m_CurYinBi:setText(formatMoneyString(roleItemManager:GetPackMoney()))
    local needChangeItemNum = roleItemManager:GetItemNumByBaseID(400354)
    self.m_CurYinBi:setText(tostring(needChangeItemNum))

    --self.m_NeedMoneyText = GameTable.common.GetCCommonTableInstance():getRecorder(433).value
    self.m_NeedYinBi = winMgr:getWindow("zhuanzhiwuqi/kuang1/di1/text")
    self.m_NeedYinBi:setText("0")

    self.m_TransfromBtn = CEGUI.toPushButton(winMgr:getWindow("zhuanzhiwuqi/btn"))
    self.m_TransfromBtn:subscribeEvent("MouseButtonUp", ZhuanZhiWuQiDlg.HandlerTransfromBtn, self)

    self.m_TextInfo = CEGUI.toRichEditbox(winMgr:getWindow("zhuanzhiwuqi/rest"))

    self.m_WeaponInfo = CEGUI.toRichEditbox(winMgr:getWindow("zhuanzhiwuqi/di2/wiqishuxing"))

    local tip = GameTable.message.GetCMessageTipTableInstance():getRecorder(174014)
    self.m_ShuoMing = CEGUI.toRichEditbox(winMgr:getWindow("zhuanzhiwuqi/di3/shuoming"))
    self.m_ShuoMing:AppendParseText(CEGUI.String(tip.msg))
    self.m_ShuoMing:Refresh()
    self.m_ShuoMing:getVertScrollbar():setScrollPosition(0)
    self.m_ShuoMing:setShowVertScrollbar(true)
    
    self.nItemCellSelId = 0

    self.m_CurWeaponID = -1
    self.m_NextWeaponID = -1

    self.m_LeftExchangeTimes = 0

    self.m_OldClass = 0

    --[[
       oldshapelist; // 旧造型列表
       oldschoollist; // 旧职业列表
        ]]
    local cmd1 = require "protodef.fire.pb.school.change.coldschoollist".Create()
    LuaProtocolManager.getInstance():send(cmd1)
    --剩余转换次数
    --local cmd2 = require "protodef.fire.pb.school.change.cchangeschoolextinfo".Create()
    --LuaProtocolManager.getInstance():send(cmd2)
end

function ZhuanZhiWuQiDlg:RefreshAllWeaponData()
    self.m_CurWuqi:SetImage(nil)
    self.m_CurWuqiText:setText("")
    SetItemCellBoundColorByQulityItem(self.m_CurWuqi, 0)

    self.m_NextWuqi:SetImage(nil)
    self.m_NextWuqiText:setText("")
    SetItemCellBoundColorByQulityItem(self.m_NextWuqi, 0)

    local curWeaponID = self.m_CurWeaponID
    self.m_CurWeaponID = -1
    self.m_NextWeaponID = -1

    self.m_WeaponInfo:Clear()
    self.m_WeaponInfo:Refresh()

    if self.m_LeftExchangeTimes ~= 0 then
        self.m_LeftExchangeTimes = self.m_LeftExchangeTimes - 1
    end

    --self:SetTransformTimesText()

    local roleItemManager = require("logic.item.roleitemmanager").getInstance()
    local hasChangeItemNum = roleItemManager:GetItemNumByBaseID(400354)

    self.m_CurYinBi:setText(tostring(hasChangeItemNum - self:getNeedChangeItemNum(curWeaponID)))
    self:initWeaponList(curWeaponID)
end

--curWeaponID 当前转换武器id
function ZhuanZhiWuQiDlg:initWeaponList(curWeaponID)
    self.vWeaponKey = { }
    local keys = {}
    local roleItemManager = require("logic.item.roleitemmanager").getInstance()
    --包里所有的装备id
    keys = roleItemManager:GetItemKeyListByType(keys, eItemType_EQUIP, fire.pb.item.BagTypes.BAG)
    if curWeaponID then
        local itemKey = roleItemManager:GetItemKeyByBaseID(curWeaponID)
        if itemKey then
            for i = 0, keys:size() - 1 do
                if keys[i] == itemKey then
                    keys[i] = nil
                    break
                end
            end
        end
    end
    self.vTableId = { }
    self:GetTableIdArray(keys)

    if self.m_tableview then
        self.itemOffect = self.m_tableview:getContentOffset()
    end

    if not self.m_tableview then
        local s = self.m_WuqiList:getPixelSize()
        self.m_tableview = TableView.create(self.m_WuqiList)
        self.m_tableview:setViewSize(s.width, s.height)
        self.m_tableview:setPosition(0, 0)
        self.m_tableview:setDataSourceFunc(self, ZhuanZhiWuQiDlg.tableViewGetCellAtIndex)
    end
    local len = #self.vTableId
    self.m_tableview:setCellCountAndSize(len, 370, 113)
    self.m_tableview:setContentOffset(self.itemOffect or 0)
    self.m_tableview:reloadData()

end

--武器list数据封装
function ZhuanZhiWuQiDlg:tableViewGetCellAtIndex(tableView, idx, cell)
    print("->>>>>into ZhuanZhiWuQiDlg:tableViewGetCellAtIndex")
    if not cell then
        cell = ZhuanZhiWuQiCell.CreateNewDlg(tableView.container, tableView:genCellPrefix())
        cell.btnBg:subscribeEvent("MouseClick", ZhuanZhiWuQiDlg.HandleClickedItem, self)
    end
    self:setGemCellInfo(cell, idx + 1)
    return cell
end

--封装武器
function ZhuanZhiWuQiDlg:setGemCellInfo(cell, index)
    local nTabId = self.vTableId[index]
    local itemAttrCfg = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(nTabId)
    if not itemAttrCfg then
        return
    end
    cell.btnBg:setID(nTabId)
    cell.btnBg:setID2(self.vWeaponKey[index])
    cell.name:setText(itemAttrCfg.name)
    cell.itemCell:SetImage(gGetIconManager():GetItemIconByID(itemAttrCfg.icon))
    SetItemCellBoundColorByQulityItemWithId(cell.itemCell, itemAttrCfg.id)

    if self.nItemCellSelId == 0 then
        self.nItemCellSelId = nTabId
    end

    if self.nItemCellSelId ~= nTabId then
        cell.btnBg:setSelected(false)
    else
        cell.btnBg:setSelected(true)
    end

end

--schoollist 造型列表
--classlist 职业列表
function ZhuanZhiWuQiDlg:SetOldSchoolList(schoollist, classlist)
    print("ZhuanZhiWuQiDlg:SetOldSchoolList,schoollist")
    PrintTable(schoollist, 2)
    --封装最后一次转职前的造型id
    self.m_OldClass = schoollist[#schoollist]

    self:initWeaponList()
end

--vGemKey:包内武器列表id
function ZhuanZhiWuQiDlg:GetTableIdArray(vGemKey)
    local roleItemManager = require("logic.item.roleitemmanager").getInstance()
    for i = 0, vGemKey:size() - 1 do
        local baggem = roleItemManager:FindItemByBagAndThisID(vGemKey[i], fire.pb.item.BagTypes.BAG)
        if baggem then
            local nTableId = baggem:GetObjectID()
            local itemAttrCfg = baggem:GetBaseObject()
            --所转换的武器必须是原角色使用武器
            local wt = LastClassForWeaponType[self.m_OldClass]
			-- LogInfo("wuqizaoxing"..wt)
			-- LogInfo("m_OldClass"..self.m_OldClass)
			local Shape = BeanConfigManager.getInstance():GetTableByName("npc.cnpcshape"):getRecorder(gGetDataManager():GetMainCharacterShape())
			LogInfo("wuqizaoxing"..Shape.id)
			self.m_NextWeaponID = math.floor((itemAttrCfg.id / 1000000)) * 1000000 + itemAttrCfg.level * 1000 + 100 + ShapeWithWeaponNumber[Shape.id]
            --包里装备类型等于wt武器类型  && 装备大于等于可转换等级
			if self:IsInTable(itemAttrCfg.itemtypeid,ZhuanZhiType)and itemAttrCfg.level >= self.needlv and nTableId ~= self.m_NextWeaponID then
                self.vTableId[#self.vTableId + 1] = nTableId
                self.vWeaponKey[#self.vWeaponKey + 1] = vGemKey[i]
            end
            -- if itemAttrCfg.itemtypeid == wt and itemAttrCfg.level >= self.needlv then
                -- self.vTableId[#self.vTableId + 1] = nTableId
                -- self.vWeaponKey[#self.vWeaponKey + 1] = vGemKey[i]
            -- end

        end
    end
end
function ZhuanZhiWuQiDlg:IsInTable(value, tbl)
for k,v in ipairs(tbl) do
  if v == value then
  return true;
  end
end
return false;
end
--p:当前选择的武器id
function ZhuanZhiWuQiDlg:setNeedChangeItemNum(p)
    local needNum = self:getNeedChangeItemNum(p)
    self.m_NeedYinBi:setText(tostring(needNum))
end

--计算转换需要多少封印石 p:当前选择的武器id
function ZhuanZhiWuQiDlg:getNeedChangeItemNum(p)
    print("into ZhuanZhiWuQiDlg:getNeedChangeItemNum(p)>>>>>>>>>>>>>"..tostring(self.needlv))
    local result
    local itemAttrCfg1 = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(p)
    if not itemAttrCfg1 then
        return
    end
    local level = itemAttrCfg1.level
    print("ZhuanZhiWuQiDlg:getNeedChangeItemNum,self.needlv=" .. tostring(self.needlv))
    print("ZhuanZhiWuQiDlg:getNeedChangeItemNum,level=" .. (((level - self.needlv) / 10) - 1))
    --武器消耗，260级以下消耗100个灵宝碎片，270消耗250个，280消耗500个
    if level <= self.needlv then
        result = 100
    else
        local limit = ((level - self.needlv) / 10) - 1
        result = (250 * limit) + 250
    end
    return result
end

function ZhuanZhiWuQiDlg:HandleClickedItem(e)
    print("into ZhuanZhiWuQiDlg:HandleClickedItem(e)>>>>>>>>>>>>")
    local mouseArgs = CEGUI.toMouseEventArgs(e)
    local id = mouseArgs.window:getID()
    local key = mouseArgs.window:getID2()
    --装备属性
    local itemAttrCfg1 = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(id)
    --当前角色模型
    local Shape = BeanConfigManager.getInstance():GetTableByName("npc.cnpcshape"):getRecorder(gGetDataManager():GetMainCharacterShape())
    if not itemAttrCfg1 then
        return
    end

    --计算转换需要多少材料
    self:setNeedChangeItemNum(id)

    self.m_CurWeaponID = id
    --self.m_NextWeaponID = (itemAttrCfg1.nquality + 3) * 1000000 + itemAttrCfg1.level * 1000 + 100 + WeaponNumber[SchoolWithShape[Shape.id]]
    self.m_NextWeaponID = math.floor((itemAttrCfg1.id / 1000000)) * 1000000 + itemAttrCfg1.level * 1000 + 100 + ShapeWithWeaponNumber[Shape.id]
    print("self.m_NextWeaponID---------->" .. self.m_NextWeaponID)

    self.m_CurWuqi:SetImage(gGetIconManager():GetItemIconByID(itemAttrCfg1.icon))
    self.m_CurWuqiText:setText(itemAttrCfg1.name)
    SetItemCellBoundColorByQulityItemWithId(self.m_CurWuqi, itemAttrCfg1.id)

    local itemAttrCfg2 = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(self.m_NextWeaponID)
    if not itemAttrCfg2 then
        return
    end

    self.m_NextWuqi:SetImage(gGetIconManager():GetItemIconByID(itemAttrCfg2.icon))
    self.m_NextWuqiText:setText(itemAttrCfg2.name)
    SetItemCellBoundColorByQulityItemWithId(self.m_NextWuqi, itemAttrCfg2.id)

    self:ShowWeaponProperty(key)

end

function ZhuanZhiWuQiDlg:ShowWeaponProperty(wid)
    self.m_WeaponInfo:Clear()

    local Itemkey = wid
    --	for i = 1, #self.vTableId do
    --		if self.vTableId[i] == wid then
    --			Itemkey = self.vWeaponKey[i]
    --			break
    --		end
    --	end

    local roleItemManager = require("logic.item.roleitemmanager").getInstance()
    local pItem = roleItemManager:FindItemByBagAndThisID(Itemkey, fire.pb.item.BagTypes.BAG)
    local pObj = nil
    if pItem then
        pObj = pItem:GetObject()
        local vcBaseKey = pObj:GetBaseEffectAllKey()
        local color = CEGUI.ColourRect(CEGUI.PropertyHelper:stringToColour("ff261407"))
        local color_blue = CEGUI.ColourRect(CEGUI.PropertyHelper:stringToColour("FF00b1ff"))
        for nIndex = 1, #vcBaseKey do
            local nBaseId = vcBaseKey[nIndex]
            local nBaseValue = pObj:GetBaseEffect(nBaseId)

            if nBaseValue ~= 0 then
                local propertyCfg = BeanConfigManager.getInstance():GetTableByName("item.cattributedesconfig"):getRecorder(nBaseId)
                if propertyCfg and propertyCfg.id ~= -1 then
                    local strTitleName = propertyCfg.name
                    local nValue = math.abs(nBaseValue)
                    if nBaseValue > 0 then
                        strTitleName = strTitleName .. " " .. "+" .. tostring(nValue)
                    elseif nBaseValue < 0 then
                        strTitleName = strTitleName .. " " .. "-" .. tostring(nValue)
                    end
                    strTitleName = "  " .. strTitleName
                    strTitleName = CEGUI.String(strTitleName)
                    self.m_WeaponInfo:AppendText(strTitleName, color)
                    self.m_WeaponInfo:AppendBreak()
                end
            end
        end

        local vPlusKey = pObj:GetPlusEffectAllKey()
        for nIndex = 1, #vPlusKey do
            local nPlusId = vPlusKey[nIndex]
            local mapPlusData = pObj:GetPlusEffect(nPlusId)
            if mapPlusData.attrvalue ~= 0 then

                local nPropertyId = mapPlusData.attrid
                local nPropertyValue = mapPlusData.attrvalue
                local propertyCfg = BeanConfigManager.getInstance():GetTableByName("item.cattributedesconfig"):getRecorder(nPropertyId)
                if propertyCfg and propertyCfg.id ~= -1 then
                    local strTitleName = propertyCfg.name
                    local nValue = math.abs(nPropertyValue)
                    if nPropertyValue > 0 then
                        strTitleName = strTitleName .. " " .. "+" .. tostring(nValue)
                    else
                        strTitleName = strTitleName .. " " .. "-" .. tostring(nValue)
                    end
                    local strEndSpace = "  "
                    local strBeginSpace = "  "
                    strTitleName = strTitleName .. strEndSpace
                    strTitleName = strBeginSpace .. strTitleName

                    strTitleName = CEGUI.String(strTitleName)
                    self.m_WeaponInfo:AppendText(strTitleName, color_blue)
                end
            end
            -- if end
        end
        -- for end

        --特技特效展示
        local nTejiId = pObj.skillid
        local nTexiaoId = pObj.skilleffect
        local strTitleColor = "ffff35fd" -- "ff29bbf7"

        local texiaoPropertyCfg = BeanConfigManager.getInstance():GetTableByName("skill.cequipskill"):getRecorder(nTexiaoId)
        if texiaoPropertyCfg and texiaoPropertyCfg.id ~= -1 then
            local strTexiaozi = MHSD_UTILS.get_resstring(11003)
            local strTexiaoName = texiaoPropertyCfg.name
            self.m_WeaponInfo:AppendText(CEGUI.String(""))
            self.m_WeaponInfo:AppendBreak()

            self.m_WeaponInfo:AppendText(CEGUI.String("  " ..strTexiaoName), CEGUI.ColourRect(CEGUI.PropertyHelper:stringToColour(strTitleColor)))
            --self.m_WeaponInfo:AppendBreak()
        end
        --CEquipSkill
        --GetCEquipSkillInfoTableInstance
        local tejiPropertyCfg = BeanConfigManager.getInstance():GetTableByName("skill.cequipskill"):getRecorder(nTejiId)
        if tejiPropertyCfg and tejiPropertyCfg.id ~= -1 then
            local strTejizi = MHSD_UTILS.get_resstring(11002)
            local strTjiName = tejiPropertyCfg.name
            self.m_WeaponInfo:AppendText(CEGUI.String(""))
            self.m_WeaponInfo:AppendBreak()
            self.m_WeaponInfo:AppendText(CEGUI.String("  " ..strTjiName), CEGUI.ColourRect(CEGUI.PropertyHelper:stringToColour(strTitleColor)))
            self.m_WeaponInfo:AppendBreak()
            --local strbuilder = StringBuilder:new()
            --strbuilder:Set("parameter1", tejiPropertyCfg.costnum)
            --local content = strbuilder:GetString(tejiPropertyCfg.cost)
            --strbuilder:delete()
            --self.m_WeaponInfo:AppendText(CEGUI.String(content), CEGUI.ColourRect(CEGUI.PropertyHelper:stringToColour(strNormalColor)))
            --self.m_WeaponInfo:AppendText(CEGUI.String(strTjiName), CEGUI.ColourRect(CEGUI.PropertyHelper:stringToColour(strNormalColor)))
            --self.m_WeaponInfo:AppendBreak()
        end

        self.m_WeaponInfo:Refresh()
    end
end

function ZhuanZhiWuQiDlg:SetTransformTimes(times)
    self.m_LeftExchangeTimes = times
    self:SetTransformTimesText()
end

function ZhuanZhiWuQiDlg:SetTransformTimesText()
    local tip = GameTable.message.GetCMessageTipTableInstance():getRecorder(174013)

    local sb = require "utils.stringbuilder":new()
    sb:Set("parameter1", self.m_LeftExchangeTimes)
    local strmsg = sb:GetString(tip.msg)
    sb:delete()

    self.m_TextInfo:Clear()
    self.m_TextInfo:AppendParseText(CEGUI.String(strmsg), false)
    self.m_TextInfo:Refresh()
end

--确认转换武器
function ZhuanZhiWuQiDlg:HandlerTransfromBtn(e)
    if self.m_CurWeaponID == -1 then
        GetCTipsManager():AddMessageTipById(174023)
        return
    end

    if self.m_NextWeaponID == -1 then
        GetCTipsManager():AddMessageTipById(174023)
        return
    end

    local roleItemManager = require("logic.item.roleitemmanager").getInstance()
    local hasChangeItemNum = roleItemManager:GetItemNumByBaseID(400354)
    --TODO 武器消耗，260级以下消耗100个灵宝碎片，270消耗250个，280消耗500个  400354	灵宝碎片
    print("tonumber(self.m_NeedYinBi:getText())  " .. tonumber(self.m_NeedYinBi:getText()) .. " hasChangeItemNum:" .. hasChangeItemNum)
    if hasChangeItemNum < tonumber(self.m_NeedYinBi:getText()) then
        print("into hasChangeItemNum < self.m_NeedYinBi:getText()")
        GetCTipsManager():AddMessageTipById(191101)
        return
    end

    --转换次数
    --if self.m_LeftExchangeTimes == 0 then
    --    GetCTipsManager():AddMessageTipById(174010)
    --    return
    --end

    local dlg = require "logic.zhuanzhi.zhuanzhiwuqiconfrimdlg".getInstanceAndShow()
    if dlg then
        print("do dlg:SetInfoData")
        local itemAttrCfg1 = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(self.m_CurWeaponID)
        if not itemAttrCfg1 then
            return
        end

        local itemAttrCfg2 = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(self.m_NextWeaponID)
        if not itemAttrCfg2 then
            return
        end

        local Itemkey = 0
        for i = 1, #self.vTableId do
            if self.vTableId[i] == self.m_CurWeaponID then
                Itemkey = self.vWeaponKey[i]
                break
            end
        end
        dlg:SetInfoData(itemAttrCfg1.name, itemAttrCfg2.name, Itemkey, self.m_NextWeaponID)
    end
    self:SetZhuanZhiWuQiDlgVisible(false)
end

function ZhuanZhiWuQiDlg:SetZhuanZhiWuQiDlgVisible(bVisible)
    self:SetVisible(bVisible)
end

return ZhuanZhiWuQiDlg