require "utils.tableutil"
CRequestClanAim = {}
CRequestClanAim.__index = CRequestClanAim



CRequestClanAim.PROTOCOL_TYPE = 808479

function CRequestClanAim.Create()
	print("enter CRequest<PERSON>lanAim create")
	return CRequestClanAim:new()
end
function CRequest<PERSON>lanAim:new()
	local self = {}
	setmetatable(self, CRequestClanAim)
	self.type = self.PROTOCOL_TYPE
	self.clanid = 0

	return self
end
function CRequestClanAim:encode()
	local os = FireNet.Marshal.OctetsStream:new()
	os:compact_uint32(self.type)
	local pos = self:marshal(nil)
	os:marshal_octets(pos:getdata())
	pos:delete()
	return os
end
function CRequestClanAim:marshal(ostream)
	local _os_ = ostream or FireNet.Marshal.OctetsStream:new()
	_os_:marshal_int64(self.clanid)
	return _os_
end

function CRequest<PERSON>lanAim:unmarshal(_os_)
	self.clanid = _os_:unmarshal_int64()
	return _os_
end

return CRequestClanAim
