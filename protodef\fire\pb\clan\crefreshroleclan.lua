require "utils.tableutil"
CRefreshRoleClan = {}
CRefreshR<PERSON>Clan.__index = CRefreshRoleClan



CRefreshRoleClan.PROTOCOL_TYPE = 808518

function CRefreshRoleClan.Create()
	print("enter CRefreshRole<PERSON>lan create")
	return CRefreshRoleClan:new()
end
function CRefresh<PERSON><PERSON><PERSON>lan:new()
	local self = {}
	setmetatable(self, CRefreshR<PERSON>Clan)
	self.type = self.PROTOCOL_TYPE
	return self
end
function CRefreshRole<PERSON>lan:encode()
	local os = FireNet.Marshal.OctetsStream:new()
	os:compact_uint32(self.type)
	local pos = self:marshal(nil)
	os:marshal_octets(pos:getdata())
	pos:delete()
	return os
end
function CRefreshRoleClan:marshal(ostream)
	local _os_ = ostream or FireNet.Marshal.OctetsStream:new()
	return _os_
end

function CRefreshRoleClan:unmarshal(_os_)
	return _os_
end

return CRefreshRole<PERSON>lan
