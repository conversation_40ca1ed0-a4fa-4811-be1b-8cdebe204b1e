require "logic.dialog"

kuaijiegongneng = {}
setmetatable(kuaijiegongneng, Dialog)
kuaijiegongneng.__index = kuaijiegongneng

local _instance
function kuaijiegongneng.getInstance()
	if not _instance then
		_instance = kuaijiegongneng:new()
		_instance:OnCreate()
	end
	return _instance
end

function kuaijiegongneng.getInstanceAndShow()
	if not _instance then
		_instance = kuaijiegongneng:new()
		_instance:OnCreate()
	else
		_instance:SetVisible(true)
	end
	return _instance
end

function kuaijiegongneng.getInstanceNotCreate()
	return _instance
end

function kuaijiegongneng.DestroyDialog()
	if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
end

function kuaijiegongneng.ToggleOpenClose()
	if not _instance then
		_instance = kuaijiegongneng:new()
		_instance:OnCreate()
	else
		if _instance:IsVisible() then
			_instance:SetVisible(false)
		else
			_instance:SetVisible(true)
		end
	end
end

function kuaijiegongneng.GetLayoutFileName()
	return "messageboxfuwul.layout"
end

function kuaijiegongneng:new()
	local self = {}
	self = Dialog:new()
	setmetatable(self, kuaijiegongneng)
	return self
end

function kuaijiegongneng:OnCreate()
	Dialog.OnCreate(self)
	local winMgr = CEGUI.WindowManager:getSingleton()

    self.m_btnFanhui = CEGUI.toPushButton(winMgr:getWindow("kuaijieanliu/fanhui"))--职业转换
	self.m_btnVip0 = CEGUI.toPushButton(winMgr:getWindow("kuaijieanliu/bbjs1"))--更新公告
	self.m_btnVip1 = CEGUI.toPushButton(winMgr:getWindow("kuaijieanliu/bbjs11"))--VIP地图
	self.m_btnVip2 = CEGUI.toPushButton(winMgr:getWindow("kuaijieanliu/bbjs12"))--快捷商城
    self.m_btnVip3 = CEGUI.toPushButton(winMgr:getWindow("kuaijieanliu/bbjs"))--神兽提升
    self.m_btnVip4 = CEGUI.toPushButton(winMgr:getWindow("kuaijieanliu/wqzh"))--武器转换
    self.m_btnVip5 = CEGUI.toPushButton(winMgr:getWindow("kuaijieanliu/zbhq"))--装备熔炼
    self.m_btnVip6 = CEGUI.toPushButton(winMgr:getWindow("kuaijieanliu/bszh"))--宝石转换
    self.m_btnVip7 = CEGUI.toPushButton(winMgr:getWindow("kuaijieanliu/ric"))--套装点化
    self.m_btnVip8 = CEGUI.toPushButton(winMgr:getWindow("kuaijieanliu/sssc"))--神兽商城
    self.m_btnVip9 = CEGUI.toPushButton(winMgr:getWindow("kuaijieanliu/zbxl"))--版本攻略
	self.m_btnVip10 = CEGUI.toPushButton(winMgr:getWindow("kuaijieanliu/zbcz"))--装备重铸
	self.m_btnguanbi = CEGUI.toPushButton(winMgr:getWindow("kuaijieanliu/back"))--关闭按钮
	
	
	
    self.m_btnFanhui:subscribeEvent("Clicked", kuaijiegongneng.zhiyezhuanhuan, self)--职业转换
	self.m_btnVip0:subscribeEvent("Clicked", kuaijiegongneng.gonggaojiemian, self) --更新公告
	self.m_btnVip1:subscribeEvent("Clicked", kuaijiegongneng.vipfuliditu, self) --VIP地图
	self.m_btnVip2:subscribeEvent("Clicked", kuaijiegongneng.kuaijieshangdian, self) --快捷商城
    self.m_btnVip3:subscribeEvent("Clicked", kuaijiegongneng.shenshoutisheng, self) --神兽提升
    self.m_btnVip4:subscribeEvent("Clicked", kuaijiegongneng.wuqizhuanhuan, self)--武器转换
	self.m_btnVip5:subscribeEvent("Clicked", kuaijiegongneng.zhuangbeironglian, self)--装备熔炼
	self.m_btnVip6:subscribeEvent("Clicked", kuaijiegongneng.baoshizhuanhuan, self)--宝石转换
	self.m_btnVip7:subscribeEvent("Clicked", kuaijiegongneng.zhuangbeidianhua, self)--套装点化
	self.m_btnVip8:subscribeEvent("Clicked", kuaijiegongneng.shenshoushangcheng, self)--神兽商城
	self.m_btnVip9:subscribeEvent("Clicked", kuaijiegongneng.banbengonglue, self)--版本攻略
	self.m_btnVip10:subscribeEvent("Clicked", kuaijiegongneng.zhuangbeichongzhu, self)--装备重铸
	self.m_btnguanbi:subscribeEvent("Clicked", kuaijiegongneng.handleQuitBtnClicked, self)--关闭按钮
    self:GetWindow():subscribeEvent("ZChanged", kuaijiegongneng.handleZchange, self)
	
	
	
	
    self.m_text = winMgr:getWindow("kuaijieanliu/text")
    self.movingToFront = false
    self:refreshbtn()
end
function kuaijiegongneng:refreshbtn()
    local funopenclosetype = require("protodef.rpcgen.fire.pb.funopenclosetype"):new()
    local manager = require "logic.pointcardserver.pointcardservermanager".getInstanceNotCreate()
    if manager then
        if manager.m_OpenFunctionList.info then
            for i,v in pairs(manager.m_OpenFunctionList.info) do
                if v.key == funopenclosetype.FUN_CHECKPOINT then
                    if v.state == 1 then
                        self.m_btnBuyOrSell:setVisible(false)
                        self.m_text:setText(MHSD_UTILS.get_resstring(11594))
                        break
                    end
                end
            end
        end
    end
end
function kuaijiegongneng:handleZchange(e)
    if not self.movingToFront then
        self.movingToFront = true
        if self:GetWindow():getParent() then
            local drawList = self:GetWindow():getParent():getDrawList()
            if drawList:size() > 0 then
                local topWnd = drawList[drawList:size()-1]
                local wnd = tolua.cast(topWnd, "CEGUI::Window")
                if wnd:getName() == "NewsWarn" then
                    if drawList:size() > 2 then
                        local secondWnd = drawList[drawList:size()-1]
                        self:GetWindow():getParent():bringWindowAbove(self:GetWindow(), tolua.cast(secondWnd, "CEGUI::Window"))
                    end
                else
                    self:GetWindow():getParent():bringWindowAbove(self:GetWindow(), tolua.cast(topWnd, "CEGUI::Window"))
                end
                
            end
        end
        self.movingToFront = false
    end
end
function kuaijiegongneng:handleQuitBtnClicked(e)
if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
end

function kuaijiegongneng:shenshoushangcheng(e) --装备熔炼
 require"logic.workshop.zhuangbeiqh".getInstanceAndShow()--装备熔炼
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end

function kuaijiegongneng:banbengonglue(e) --版本攻略
 require"logic.workshop.zhuangbeifumo".getInstanceAndShow() --攻略界面	
  if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
	
end 

function kuaijiegongneng:shenshoutisheng(e) --神兽提升
 require"logic.pet.shenshoucommon".Increase(itemname, npckey, needpetbaseid)	--神兽提升
  if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
	
end

function kuaijiegongneng:gonggaojiemian(e) --更新公告
 require"logic.item.xilian.zhuangbeixiliandlg".getInstanceAndShow() --公告界面	
  if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
	
end 

function kuaijiegongneng:vipfuliditu(e) --VIP地图
 require"logic.workshop.superronglian".getInstanceAndShow() --地图界面	
  if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
	
end

function kuaijiegongneng:zhuangbeichongzhu(e) --装备重铸
 require"logic.workshop.workshopxilian".getInstanceAndShow() --重铸
  if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
	
end

function kuaijiegongneng:kuaijieshangdian(e) --快捷商城
 require"logic.workshop.workshopaq".getInstanceAndShow() --装备进阶
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end

function kuaijiegongneng:wuqizhuanhuan(e) --武器转换
 require"logic.zhuanzhi.zhuanzhiwuqidlg".getInstanceAndShow() --武器转换
  if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
	
end
function kuaijiegongneng:zhiyezhuanhuan(e) --职业转换
 		 ZhuanZhiDlg.getInstanceAndShow() --职业转换
  if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
	
end
function kuaijiegongneng:zhuangbeironglian(e) --装备熔炼
 require"logic.zhuanzhi.equipupgradedlg".getInstanceAndShow() --纹饰进阶
  if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
	
end
function kuaijiegongneng:baoshizhuanhuan(e) --宝石转换
  ZhuanZhiBaoShi.getInstanceAndShow() --宝石转换
  if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
	
end
function kuaijiegongneng:zhuangbeidianhua(e) --套装点化
 require"logic.workshop.Attunement".getInstanceAndShow() --套装点化
  if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
	
end
function kuaijiegongneng:handleAddtBtnClicked(e)
    self:GetWindow():setVisible(false)
    require("logic.shop.shoplabel").showRecharge()
end
function kuaijiegongneng:Show()
    self:GetWindow():setVisible(true)
end
return kuaijiegongneng