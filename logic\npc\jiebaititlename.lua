require "logic.dialog"

jieBaiSetTitleName = {}
setmetatable(jieBaiSetTitleName, Dialog)
jieBaiSetTitleName.__index = jieBaiSetTitleName


local _instance
function jieBaiSetTitleName.getInstance()
	if not _instance then
		_instance = jieBaiSetTitleName:new()
		_instance:OnCreate()
	end
	return _instance
end

function jieBaiSetTitleName.getInstanceAndShow()
	if not _instance then
		_instance = jieBaiSetTitleName:new()
		_instance:OnCreate()
	else
		_instance:SetVisible(true)
	end
	return _instance
end

function jieBaiSetTitleName.getInstanceNotCreate()
	return _instance
end

function jieBaiSetTitleName.DestroyDialog()
	if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
end

function jieBaiSetTitleName.ToggleOpenClose()
	if not _instance then
		_instance = jieBaiSetTitleName:new()
		_instance:OnCreate()
	else
		if _instance:IsVisible() then
			_instance:SetVisible(false)
		else
			_instance:SetVisible(true)
		end
	end
end

function jieBaiSetTitleName.GetLayoutFileName()
	return "jiebaititlename.layout"
end

function jieBaiSetTitleName:new()
	local self = {}
	self = Dialog:new()
	setmetatable(self, jieBaiSetTitleName)
	return self
end

function jieBaiSetTitleName:OnCreate( )
	Dialog.OnCreate(self)
	local winMgr = CEGUI.WindowManager:getSingleton()

	self.jieBaiSetTitleName = winMgr:getWindow("jiebaititlename")
	self.frameWindow = CEGUI.toFrameWindow(winMgr:getWindow("jiebaititlename/framewindow"))
	self.descText = winMgr:getWindow("jiebaititlename/text")
	self.inputBox = CEGUI.toEditbox(winMgr:getWindow("jiebaititlename/shurubg/editbox"))
    self.inputBox:subscribeEvent("KeyboardTargetWndChanged", jieBaiSetTitleName.HandleKeyboardTargetWndChanged, self)
	self.placeHolder = winMgr:getWindow("jiebaititlename/shurubg/textqing")
	self.cancelBtn = CEGUI.toPushButton(winMgr:getWindow("jiebaititlename/btnquxiao"))
	self.sureBtn = CEGUI.toPushButton(winMgr:getWindow("jiebaititlename/queren"))

	self.cancelBtn:subscribeEvent("Clicked", jieBaiSetTitleName.handleCancelClicked, self)
	self.sureBtn:subscribeEvent("Clicked", jieBaiSetTitleName.handleSureClicked, self)

    self.inputBox:SetNormalColourRect(0xffffffff);

    self.inputBox:SetShieldSpace(true)		--����������ո�
	self.inputBox:SetOnlyNumberMode(false,-1)
	self.inputBox:subscribeEvent("TextChanged", self.OnEditNumChange, self)
end

function jieBaiSetTitleName:handleCancelClicked(args)
    jieBaiSetTitleName.DestroyDialog()
	return true
end

function jieBaiSetTitleName:handleSureClicked(args)
    if MHSD_UTILS.ShiedText(self.inputBox:getText()) then
	    GetCTipsManager():AddMessageTipById(142260) --�����к��зǷ��ַ������������롣
	    return true
    end
	local size = GetTeamManager():GetTeamMemberNum()
	if size < 3 then
		GetCTipsManager():AddMessageTipById(191206) --�����к��зǷ��ַ������������롣
	    return true
	end

    local p = require "protodef.fire.pb.npc.csetjiebaititlename".new()
	p.jiebaititlename = self.inputBox:getText()
	require "manager.luaprotocolmanager":send(p)
    jieBaiSetTitleName.DestroyDialog()
    return true
end

function jieBaiSetTitleName:OnEditNumChange()
    if string.len(self.inputBox:getText()) > 0 then
	    self.sureBtn:setEnabled(true)
    else
		self.sureBtn:setEnabled(false);
    end
end
function jieBaiSetTitleName:HandleKeyboardTargetWndChanged(args)
    local wnd = CEGUI.toWindowEventArgs(args).window
    if wnd == self.inputBox then
        self.placeHolder:setVisible(false)
    else
        if self.inputBox:getText() == "" then
            self.placeHolder:setVisible(true)
        end
    end
end
return jieBaiSetTitleName
