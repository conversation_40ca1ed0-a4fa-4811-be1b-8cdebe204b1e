local p = require "protodef.fire.pb.skill.ssendassistskillmaxlevels"
function p:process()
end

local supdateinborn = require "protodef.fire.pb.skill.supdateinborn"
function supdateinborn:process()
    for acuPointID, acuPointLevel in pairs(self.inborns) do
        RoleSkillManager.getInstance():UpdateAcupointLevel(acuPointID, acuPointLevel)
    end
	local CharacterSkillUpdateDlg = require "logic.skill.characterskillupdatedlg".getInstanceOrNot()
	if CharacterSkillUpdateDlg then
		CharacterSkillUpdateDlg:updateSkillLevel(self.inborns)
	end

    YangChengListDlg.dealwithSkillPoint()
end

local ssendinborns = require "protodef.fire.pb.skill.ssendinborns"
function ssendinborns:process()
    for nKey,nValue in pairs(self.inborns) do 
       RoleSkillManager.getInstance():InsertAcupointInfo(nKey,nValue)
    end
    RoleSkillManager.getInstance():UpdateRoleSkillAndAcupoint()
	
end


local supdateextskill = require "protodef.fire.pb.skill.supdateextskill"
function supdateextskill:process()
    RoleSkillManager.getInstance():ClearExtSkill()
    for nKey,nValue in pairs(self.extskilllists) do
        RoleSkillManager.getInstance():InsertExtSkill(nKey,nValue)
    end
    local nSkillNum = require "utils.tableutil".tablelength(self.extskilllists)
    if GetBattleManager() and nSkillNum==0 then
        GetBattleManager():SetFirstShowQuickButton(false)
    end
end

local ssendspecialskills = require "protodef.fire.pb.skill.ssendspecialskills"
function ssendspecialskills:process()
    RoleSkillManager.getInstance():ClearEquipSkillMap()
    RoleSkillManager.getInstance():InsertEquipSkillMap(self.skills,self.effects)
end

