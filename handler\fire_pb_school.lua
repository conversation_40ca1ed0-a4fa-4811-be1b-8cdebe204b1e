--region *.lua
--Date
--此文件由[BabeLua]插件自动生成
local m = require "protodef.fire.pb.school.change.soldschoollist"
function m:process()
    local dlg = ZhuanZhiDlg.getInstanceNotCreate()
    if dlg then
        dlg:SetOld<PERSON>chool<PERSON>ist(self.oldshapelist, self.oldschoollist)
    end
    local dlg2 = ZhuanZhiWuQiDlg.getInstanceNotCreate()
    if dlg2 then
        dlg2:SetOld<PERSON>choolList(self.oldshapelist, self.oldschoollist)
    end
end

m = require("protodef.fire.pb.school.change.schangegem")
function m:process()
    GetCTipsManager():AddMessageTipById(191160)
end

m = require("protodef.fire.pb.school.change.schangeweapon")
function m:process()
    GetCTipsManager():AddMessageTipById(191161)
end


--endregion
