	------------------------------------------------------------------
	-- NPC�����̵�
	------------------------------------------------------------------
	require "logic.dialog"

	CommerceSellDlg = {}
	setmetatable(CommerceSellDlg, Dialog)
	CommerceSellDlg.__index = CommerceSellDlg

	local _instance
	function CommerceSellDlg.getInstance()
		if not _instance then
			_instance = CommerceSellDlg:new()
			_instance:OnCreate()
		end
		return _instance
	end

	function CommerceSellDlg.getInstanceAndShow()
		if not _instance then
			_instance = CommerceSellDlg:new()
			_instance:OnCreate()
		else
			_instance:SetVisible(true)
		end
		return _instance
	end

	function CommerceSellDlg.getInstanceOrNot()
		return _instance
	end

	function CommerceSellDlg.DestroyDialog()
		if _instance then 
			if not _instance.m_bCloseIsHide then
				_instance:OnClose()
				_instance = nil
			else
				_instance:ToggleOpenClose()
			end
		end
	end

	function CommerceSellDlg:OnClose()
		CurrencyManager.unregisterTextWidget(_instance.ownMoneyText)
		gGetRoleItemManager():RemoveLuaItemNumChangeNotify(_instance.eventItemNumChange)
		Dialog.OnClose(_instance)
		_instance = nil
	end

	function CommerceSellDlg.ToggleOpenClose()
		if not _instance then
			_instance = CommerceSellDlg:new()
			_instance:OnCreate()
		else
			if _instance:IsVisible() then
				_instance:SetVisible(false)
			else
				_instance:SetVisible(true)
			end
		end
	end

	function CommerceSellDlg.GetLayoutFileName()
		return "shanghuichushou_mtg.layout"
	end

	function CommerceSellDlg:new()
		local self = {}
		self = Dialog:new()
		setmetatable(self, CommerceSellDlg)
		return self
	end

	function CommerceSellDlg:OnCreate()
		Dialog.OnCreate(self)
		SetPositionOfWindowWithLabel(self:GetWindow())
		local winMgr = CEGUI.WindowManager:getSingleton()

		self.numText = winMgr:getWindow("shanghuichushou_mtg/shurukuang/buynum")
		self.priceText = winMgr:getWindow("shanghuichushou_mtg/textzong")
		self.ownMoneyText = winMgr:getWindow("shanghuichushou_mtg/textdan")
		self.buyBtn = CEGUI.toPushButton(winMgr:getWindow("shanghuichushou_mtg/btngoumai"))
		self.saleBth = CEGUI.toPushButton(winMgr:getWindow("shanghuichushou_mtg/yijiansale"))
		self.sellItemScroll = CEGUI.toScrollablePane(winMgr:getWindow("shanghuichushou_mtg/textbg3/list2"))
		self.sellItemTable = CEGUI.toItemTable(winMgr:getWindow("shanghuichushou_mtg/textbg3/list2/table"))
		self.minusBtn = CEGUI.toPushButton(winMgr:getWindow("shanghuichushou_mtg/btnjianhao"))
		self.addBtn = CEGUI.toPushButton(winMgr:getWindow("shanghuichushou_mtg/jiahao"))

		self.sellItemScroll:EnableAllChildDrag(self.sellItemScroll)

		self.sellItemTable:subscribeEvent("TableClick", CommerceSellDlg.handleItemClicked, self)
		self.buyBtn:subscribeEvent("Clicked", CommerceSellDlg.handleBuyClicked, self)
		self.saleBth:subscribeEvent("Clicked", CommerceSellDlg.allSell, self)
		self.numText:subscribeEvent("MouseClick", CommerceSellDlg.handleNumTextClicked, self)
		self.minusBtn:subscribeEvent("Clicked", CommerceSellDlg.handleMinusClicked, self)
		self.addBtn:subscribeEvent("Clicked", CommerceSellDlg.handleAddClicked, self)

		self.eventItemNumChange = gGetRoleItemManager():InsertLuaItemNumChangeNotify(CommerceSellDlg.onEventItemNumChange)

		CurrencyManager.registerTextWidget(fire.pb.game.MoneyType.MoneyType_GoldCoin, self.ownMoneyText)

		self.selectedItemkey = 0
		self.selectedItemId = 0
		self.selectedGoodsId = 0
		self:refreshSellGoodsList()

		--�����̻���Ʒ�µļ۸�
		local p = require("protodef.fire.pb.shop.crequstshopprice"):new()
		p.shopid = SHOP_TYPE.COMMERCE
		LuaProtocolManager:send(p)

		--����޹���������
		ShopManager:checkNumLimit()
	end

	function CommerceSellDlg:allSell()
		--2.弹出确认框
	local function onConfirm(self, args)
		gGetMessageManager():CloseCurrentShowMessageBox()
		local roleItemManager = require("logic.item.roleitemmanager").getInstance()
		local roleItems = roleItemManager:FilterBagItem(eItemFilterType_CanSale, true)
		if roleItems:size() > 0 then
			local itemlist = {}
			for i=0, roleItems:size()-1 do
			local ishaveitem = 0
			local salelist = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(roleItems[i]:GetObjectID()).bCanSaleToNpc
			local conf = BeanConfigManager.getInstance():GetTableByName(CheckTableName("shop.cgoods")):getRecorder(salelist)
			local sellNum = ShopManager.goodsSellNumLimit[salelist]
			if not sellNum then
				return
			end
			local salenum = roleItems[i]:GetNum()
			if	sellNum >= conf.limitSaleNum then
				for key, value in pairs(itemlist) do
					if value == roleItems[i]:GetObjectID() then
						ishaveitem=1
					end
				end
				if ishaveitem == 0 then
					local str = MHSD_UTILS.get_msgtipstring(150505)
					str = string.gsub(str, "%$parameter1%$", conf.limitSaleNum)
					GetCTipsManager():AddMessageTip(str)
					table.insert(itemlist,roleItems[i]:GetObjectID())
					ishaveitem=1
				end
			end
			if ishaveitem == 0 then 
			   if	roleItems[i]:GetNum() > (conf.limitSaleNum-sellNum) then
				    salenum = conf.limitSaleNum-sellNum;
			   end
			      local p = require("protodef.fire.pb.shop.cchamberofcommerceshop"):new()
			      p.shopid = SHOP_TYPE.COMMERCE
				  p.itemkey = roleItems[i]:GetThisID()
				  p.goodsid = salelist
				  p.num = salenum
				  p.buytype = fire.pb.shop.ShopBuyType.CHAMBER_OF_COMMERCE_SHOP_SALE
				  LuaProtocolManager:send(p)
			   end
			end
		end
		return true
	end

	local function onCancel(self, args)
		if CEGUI.toWindowEventArgs(args).handled ~= 1 then
			gGetMessageManager():CloseCurrentShowMessageBox()
		end
	end

	if gGetMessageManager() then
		gGetMessageManager():AddMessageBox(
				"", MHSD_UTILS.get_msgtipstring(191228),
				onConfirm, self, onCancel, self,
				eMsgType_Normal, 30000, 0, 0, nil,
				MHSD_UTILS.get_resstring(2037),
				MHSD_UTILS.get_resstring(2038))
	end
		

	end

	function CommerceSellDlg:refreshSellGoodsList()
		local roleItemManager = require("logic.item.roleitemmanager").getInstance()
		local roleItems = roleItemManager:FilterBagItem(eItemFilterType_CanSale, true)
		
		local foundLastSelected = false
		local column = self.sellItemTable:GetColCount()
		
		if roleItems:size() > 0 then
			self.sellItemTable:setVisible(true)
			local row = math.ceil(roleItems:size() / column)
			if self.sellItemTable:GetRowCount() ~= row then
				self.sellItemTable:SetRowCount(row)
				local h = self.sellItemTable:GetCellHeight()
				local spaceY = self.sellItemTable:GetSpaceY()
				self.sellItemTable:setHeight(CEGUI.UDim(0, (h+spaceY)*row))
				self.sellItemScroll:EnableAllChildDrag(self.sellItemScroll)
			end
			
			for i=0, row*column-1 do
				local cell = self.sellItemTable:GetCell(i)
				cell:Clear()
				cell:SetHaveSelectedState(true)
				if i < roleItems:size() then
					cell:setVisible(true)
					local item = roleItems[i]
					local img = gGetIconManager():GetImageByID(item:GetIcon())
					cell:SetImage(img)
					refreshItemCellBind(cell, item:GetObject().loc.tableType, item:GetThisID())
					SetItemCellBoundColorByQulityItem(cell, item:GetBaseObject().nquality)
					
					local curNum = item:GetNum()
					cell:SetTextUnit(curNum > 1 and curNum or "")
					
					cell:setID(item:GetObjectID()) --baseid
					cell:setID2(item:GetThisID())  --itemkey
					
					if self.selectedItemkey == item:GetThisID() then
						foundLastSelected = true
						cell:SetSelected(true)
					end
				else
					cell:setVisible(false)
				end
			end
		else
			self.sellItemTable:setVisible(false)
		end
		
		if not foundLastSelected then
			self.selectedItemkey = 0
			self.selectedItemId = 0
			self.numText:setText(0)
			self.priceText:setText(0)
		end
		
		roleItems = nil
	end

	function CommerceSellDlg:handleItemClicked(args)
		local cell = CEGUI.toItemCell(CEGUI.toWindowEventArgs(args).window)
		if not cell then
			return
		end

		local itemkey = cell:getID2()
		local item = RoleItemManager.getInstance():FindItemByBagAndThisID(itemkey, fire.pb.item.BagTypes.BAG)
		if item then
			LuaShowItemTip(fire.pb.item.BagTypes.BAG, itemkey, 0, 0)
			local pos = cell:GetScreenPosOfCenter()
			local commontip = Commontipdlg.getInstanceAndShow()
			commontip:RefreshSize(false)
			commontip:RefreshPosCorrect(pos.x, pos.y)
		end

		if itemkey == self.selectedItemkey then
			return
		end
		self.selectedItemkey = itemkey

		local itemid = cell:getID()
		self.selectedItemId = itemid

		self.selectedGoodsId = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(itemid).bCanSaleToNpc
		self:refreshPriceAndNumLimit()
	end

	function CommerceSellDlg:refreshPriceAndNumLimit()
		self.numText:setText(1)

		--��������
		local num = RoleItemManager.getInstance():GetBagItem(self.selectedItemkey):GetNum()
		local goodsConf = BeanConfigManager.getInstance():GetTableByName(CheckTableName("shop.cgoods")):getRecorder(self.selectedGoodsId)
		if goodsConf and goodsConf.limitType ~= 0 then
			local sellNum = ShopManager.goodsSellNumLimit[self.selectedGoodsId]
			self.maxNum = sellNum and math.min(num, goodsConf.limitSaleNum-sellNum) or 0
		else
			self.maxNum = num
		end

		-- self.curPrice = ShopManager.goodsPrices[self.selectedGoodsId] or goodsConf.Saleprices
		self.curPrice = math.floor(goodsConf.Saleprices)--self.curPrice /100*60   4000/100 -> 40*60 -> 2400 800
		self.priceText:setText(MoneyFormat(self.curPrice))
	end

	function CommerceSellDlg:handleBuyClicked(args)
		local num = tonumber(self.numText:getText())
		if num == 0 then
			return
		end

		if self.selectedGoodsId ~= 0 then
			local conf = BeanConfigManager.getInstance():GetTableByName(CheckTableName("shop.cgoods")):getRecorder(self.selectedGoodsId)
			local sellNum = ShopManager.goodsSellNumLimit[self.selectedGoodsId]
			if not sellNum then
				return
			end
			
			if conf.limitSaleNum > 0 and sellNum >= conf.limitSaleNum then
				local str = MHSD_UTILS.get_msgtipstring(150505)
				str = string.gsub(str, "%$parameter1%$", conf.limitSaleNum)
				GetCTipsManager():AddMessageTip(str)  --�Ѵﵽ�������ޣ�����Ʒÿ�ճ�������xx��
				return
			end
			
			local p = require("protodef.fire.pb.shop.cchamberofcommerceshop"):new()
			p.shopid = SHOP_TYPE.COMMERCE
			p.itemkey = self.selectedItemkey
			p.goodsid = self.selectedGoodsId
			p.num = num
			p.buytype = fire.pb.shop.ShopBuyType.CHAMBER_OF_COMMERCE_SHOP_SALE
			LuaProtocolManager:send(p)
		end
	end

	function CommerceSellDlg:handleMinusClicked(args)
		if  self.selectedItemkey == 0 then
			return
		end
		local num = tonumber(self.numText:getText())
		if num > 1 then
			self.numText:setText(num-1)
			self.priceText:setText(MoneyFormat(self.curPrice * (num-1)))
		end
	end

	function CommerceSellDlg:handleAddClicked(args)
		if self.selectedItemkey == 0 then
			return
		end
		local num = tonumber(self.numText:getText())
		if num < self.maxNum then
			self.numText:setText(num+1)
			self.priceText:setText(MoneyFormat(self.curPrice * (num+1)))
		end
	end

	function CommerceSellDlg:handleNumTextClicked(args)
		if self.selectedItemkey == 0 then
			return
		end
		
		if NumKeyboardDlg.getInstanceNotCreate() then
			NumKeyboardDlg.getInstanceNotCreate():SetVisible(true) --���ּ�����������
			return
		end
		
		local dlg = NumKeyboardDlg.getInstanceAndShow()
		if dlg then
			dlg:setTriggerBtn(self.numText)
			dlg:setMaxValue(self.maxNum)
			dlg:setInputChangeCallFunc(CommerceSellDlg.onNumInputChanged, self)
			
			local p = self.numText:GetScreenPos()
			SetPositionOffset(dlg:GetWindow(), p.x-110, p.y-10, 0, 1)
		end
	end

	function CommerceSellDlg:onNumInputChanged(num)
		if num == 0 then
			self.numText:setText(1)
			self.priceText:setText(MoneyFormat(self.curPrice))
		else
			self.numText:setText(num)
			self.priceText:setText(MoneyFormat(self.curPrice * num))
		end
	end

	function CommerceSellDlg:onSellNumLimitChanged()
		if self.selectedItemkey == 0 then
			return
		end
		--��������
		local item = RoleItemManager.getInstance():GetBagItem(self.selectedItemkey)
		if not item then
			return
		end
		local num = item:GetNum()
		local goodsConf = BeanConfigManager.getInstance():GetTableByName(CheckTableName("shop.cgoods")):getRecorder(self.selectedGoodsId)
		if goodsConf and goodsConf.limitType ~= 0 then
			local sellNum = ShopManager.goodsSellNumLimit[self.selectedGoodsId]
			self.maxNum = sellNum and math.min(num, goodsConf.limitSaleNum-sellNum) or 0
		else
			self.maxNum = num
		end

		local curNum = tonumber(self.numText:getText())
		if curNum > 1 and curNum > self.maxNum then
			self.numText:setText(self.maxNum)
			self.priceText:setText(MoneyFormat(self.curPrice * self.maxNum))
		end
	end

	function CommerceSellDlg.onEventItemNumChange(bagid, itemkey, itembaseid)
		if not _instance or bagid ~= fire.pb.item.BagTypes.BAG then
			return
		end

		local roleItemManager = require("logic.item.roleitemmanager").getInstance()
		local item = roleItemManager:GetBagItem(itemkey)
		if item and item:GetBaseObject().bCanSaleToNpc == 0 then
			return
		end

		for i=0, _instance.sellItemTable:GetCellCount()-1 do
			local cell = _instance.sellItemTable:GetCell(i)
			if not cell:isVisible() then
				break
			end
			
			if cell:getID() == itembaseid then
				local curNum = (item and item:GetNum() or 0)
				--print('item num change', itembaseid, curNum)
				if curNum == 0 then
					_instance:refreshSellGoodsList()
				elseif itemkey == _instance.selectedItemkey then
					_instance:refreshPriceAndNumLimit()
					cell:SetTextUnit(curNum > 1 and curNum or "")
				end
				return
			end
		end

		_instance:refreshSellGoodsList()
	end

	function CommerceSellDlg.onInternetReconnected()
		if _instance then
			_instance:refreshSellGoodsList() --ˢ�³�����Ʒ�б�
		end
	end

	return CommerceSellDlg