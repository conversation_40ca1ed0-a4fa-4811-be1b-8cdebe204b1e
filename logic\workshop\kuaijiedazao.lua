require "logic.dialog"

KuaiJieDaZao = {}
setmetatable(KuaiJieDaZao, Dialog)
KuaiJieDaZao.__index = KuaiJieDaZao

local _instance
function KuaiJieDaZao.getInstance()
	if not _instance then
		_instance = KuaiJieDaZao:new()
		_instance:OnCreate()
	end
	return _instance
end

function KuaiJieDaZao.getInstanceAndShow()
	if not _instance then
		_instance = KuaiJieDaZao:new()
		_instance:OnCreate()
	else
		_instance:SetVisible(true)
	end
	return _instance
end

function Ku<PERSON>JieDaZao.getInstanceNotCreate()
	return _instance
end

function KuaiJieDaZao.DestroyDialog()
	if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
end

function KuaiJieDaZao.ToggleOpenClose()
	if not _instance then
		_instance = KuaiJieDaZao:new()
		_instance:OnCreate()
	else
		if _instance:IsVisible() then
			_instance:SetVisible(false)
		else
			_instance:SetVisible(true)
		end
	end
end

function KuaiJieDaZao.GetLayoutFileName()
	return "kuaijiedazao.layout"
end

function KuaiJieDaZao:new()
	local self = {}
	self = Dialog:new()
	setmetatable(self, KuaiJieDaZao)
	return self
end

function KuaiJieDaZao:OnCreate()
	Dialog.OnCreate(self)
	local winMgr = CEGUI.WindowManager:getSingleton()

	self.goumaidazao = CEGUI.toPushButton(winMgr:getWindow("kuaijiedazao/jiemian/goumaidazao"))
	self.text = winMgr:getWindow("kuaijiedazao/jiemian/wenben")
	self.text12 = winMgr:getWindow("kuaijiedazao/jiemian/wenben12")
	
	self.quxiao = CEGUI.toPushButton(winMgr:getWindow("kuaijiedazao/jiemian/quxiao"))
	self.goumai = CEGUI.toPushButton(winMgr:getWindow("kuaijiedazao/jiemian/goumai"))
    self.quxiao:subscribeEvent("MouseClick",KuaiJieDaZao.clickClose,self)
	self.goumai:subscribeEvent("MouseClick",KuaiJieDaZao.shopitem,self)
	self.goumaidazao:subscribeEvent("MouseClick",KuaiJieDaZao.goumaidazao,self)
end
function KuaiJieDaZao:clickClose(e)
	KuaiJieDaZao.DestroyDialog()
end
function KuaiJieDaZao:shopitem(e) 
	local dzDlg = require "logic.workshop.workshopdznew".getInstanceOrNot()
	if dzDlg then 
		dzDlg:goumaicailiao()
		KuaiJieDaZao.DestroyDialog()
	end
end
function KuaiJieDaZao:goumaidazao(e) 
	local dzDlg = require "logic.workshop.workshopdznew".getInstanceOrNot()
	if dzDlg then 
		dzDlg:goumaidazao()
		KuaiJieDaZao.DestroyDialog()
	end
end

function KuaiJieDaZao:setJieMianText(Price,item1,item2,item3) 
self.text:setText(tostring(Price))
local str = tostring(item1..item2..item3)
if string.len(str)>55 then
str = tostring(item1..item2.."\n"..item3)
end
self.text12:setText(str)
end




return KuaiJieDaZao