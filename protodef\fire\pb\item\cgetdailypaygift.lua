require "utils.tableutil"
CGetDailyPayGift = {}
CGetDailyPayGift.__index = CGetDailyPayGift



CGetDailyPayGift.PROTOCOL_TYPE = 817958

function CGetDailyPayGift.Create()
	print("enter CGetDailyPayGift create")
	return CGetDailyPayGift:new()
end
function CGetDailyPayGift:new()
	local self = {}
	setmetatable(self, CGetDailyPayGift)
	self.type = self.PROTOCOL_TYPE
	self.rewardid = 0

	return self
end
function CGetDailyPayGift:encode()
	local os = FireNet.Marshal.OctetsStream:new()
	os:compact_uint32(self.type)
	local pos = self:marshal(nil)
	os:marshal_octets(pos:getdata())
	pos:delete()
	return os
end
function CGetDailyPayGift:marshal(ostream)
	local _os_ = ostream or FireNet.Marshal.OctetsStream:new()
	_os_:marshal_char(self.rewardid)
	return _os_
end

function CGetDailyPayGift:unmarshal(_os_)
	self.rewardid = _os_:unmarshal_char()
	return _os_
end

return CGetDailyPayGift
