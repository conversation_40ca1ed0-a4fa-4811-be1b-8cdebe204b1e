require "logic.dialog"

MessageForPointCardDlg = {}
setmetatable(MessageForPointCardDlg, Dialog)
MessageForPointCardDlg.__index = MessageForPointCardDlg

local _instance
function MessageForPointCardDlg.getInstance()
	if not _instance then
		_instance = MessageForPointCardDlg:new()
		_instance:OnCreate()
	end
	return _instance
end

function MessageForPointCardDlg.getInstanceAndShow()
	if not _instance then
		_instance = MessageForPointCardDlg:new()
		_instance:OnCreate()
	else
		_instance:SetVisible(true)
	end
	return _instance
end

function MessageForPointCardDlg.getInstanceNotCreate()
	return _instance
end

function MessageForPointCardDlg.DestroyDialog()
	if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
end

function MessageForPointCardDlg.ToggleOpenClose()
	if not _instance then
		_instance = MessageForPointCardDlg:new()
		_instance:OnCreate()
	else
		if _instance:IsVisible() then
			_instance:SetVisible(false)
		else
			_instance:SetVisible(true)
		end
	end
end

function MessageForPointCardDlg.GetLayoutFileName()
	return "messageboxfuwu.layout"
end

function MessageForPointCardDlg:new()
	local self = {}
	self = Dialog:new()
	setmetatable(self, MessageForPointCardDlg)
	return self
end

function MessageForPointCardDlg:OnCreate()
	Dialog.OnCreate(self)
	local winMgr = CEGUI.WindowManager:getSingleton()

    self.m_btnQuitGame = CEGUI.toPushButton(winMgr:getWindow("messageboxfuwu/Canle"))
    self.m_btnBuyOrSell = CEGUI.toPushButton(winMgr:getWindow("messageboxfuwu/Canle1"))
	self.m_btnQuitgeren = CEGUI.toPushButton(winMgr:getWindow("messageboxfuwu/Canle11"))
    self.m_btnAdd = CEGUI.toPushButton(winMgr:getWindow("messageboxfuwu/OK"))
	self.m_btnAddchonghzhi = CEGUI.toPushButton(winMgr:getWindow("messageboxfuwu/zaixianchongzhi"))
	self.m_btnguanbi = CEGUI.toPushButton(winMgr:getWindow("messageboxfuwu/CloseButton"))
	
    self.m_btnQuitGame:subscribeEvent("Clicked", MessageForPointCardDlg.handleQuitBtnClicked, self)
    self.m_btnBuyOrSell:subscribeEvent("Clicked", MessageForPointCardDlg.handleBuyOrSellBtnClicked, self) 
	self.m_btnQuitgeren:subscribeEvent("Clicked", MessageForPointCardDlg.handleBuyOrSellBtnClickedgeren, self) 
    self.m_btnAdd:subscribeEvent("Clicked", MessageForPointCardDlg.handleAddtBtnClicked, self)
	self.m_btnAddchonghzhi:subscribeEvent("Clicked", MessageForPointCardDlg.handleAddtBtnchongzhi, self)
	self.m_btnguanbi:subscribeEvent("Clicked", MessageForPointCardDlg.handguanbi, self)
    --self:GetWindow():subscribeEvent("ZChanged", MessageForPointCardDlg.handleZchange, self)
    self.m_text = winMgr:getWindow("messageboxfuwu/text")
    self.movingToFront = false
    self:refreshbtn()
end
function MessageForPointCardDlg:refreshbtn()
    local funopenclosetype = require("protodef.rpcgen.fire.pb.funopenclosetype"):new()
    local manager = require "logic.pointcardserver.pointcardservermanager".getInstanceNotCreate()
    if manager then
        if manager.m_OpenFunctionList.info then
            for i,v in pairs(manager.m_OpenFunctionList.info) do
                if v.key == funopenclosetype.FUN_CHECKPOINT then
                    if v.state == 1 then
                        self.m_btnBuyOrSell:setVisible(false)
                        self.m_text:setText(MHSD_UTILS.get_resstring(11594))
                        break
                    end
                end
            end
        end
    end
end
function MessageForPointCardDlg:handleZchange(e)
    if not self.movingToFront then
        self.movingToFront = true
        if self:GetWindow():getParent() then
            local drawList = self:GetWindow():getParent():getDrawList()
            if drawList:size() > 0 then
                local topWnd = drawList[drawList:size()-1]
                local wnd = tolua.cast(topWnd, "CEGUI::Window")
                if wnd:getName() == "NewsWarn" then
                    if drawList:size() > 2 then
                        local secondWnd = drawList[drawList:size()-1]
                        self:GetWindow():getParent():bringWindowAbove(self:GetWindow(), tolua.cast(secondWnd, "CEGUI::Window"))
                    end
                else
                    self:GetWindow():getParent():bringWindowAbove(self:GetWindow(), tolua.cast(topWnd, "CEGUI::Window"))
                end
                
            end
        end
        self.movingToFront = false
    end
end





function MessageForPointCardDlg:handleAddtBtnchongzhi(e)
    local account = gGetLoginManager():GetAccount()
	local key = gGetLoginManager():GetPassword()
	local serverid = tostring(gGetLoginManager():getServerID())
	local roleid = tostring(gGetDataManager():GetMainCharacterID()) 
	local name = gGetDataManager():GetMainCharacterName() 
	IOS_MHSD_UTILS.OpenURL("http://106.54.45.89:88".."/user/bind.php?user="..account.."&password="..Base64.Encode(key, string.len(key)).."&serverid="..serverid.."&name="..Base64.Encode(name, string.len(name)).."&roleid="..roleid);	
end

function MessageForPointCardDlg:handleBuyOrSellBtnClickedgeren(e)
    local account = gGetLoginManager():GetAccount()
	local key = gGetLoginManager():GetPassword()
	local serverid = tostring(gGetLoginManager():getServerID())
	local roleid = tostring(gGetDataManager():GetMainCharacterID()) 
	local name = gGetDataManager():GetMainCharacterName() 
	IOS_MHSD_UTILS.OpenURL("http://106.54.45.89:88".."/user/bind.php?user="..account.."&password="..Base64.Encode(key, string.len(key)).."&serverid="..serverid.."&name="..Base64.Encode(name, string.len(name)).."&roleid="..roleid);	
end


function MessageForPointCardDlg:handleQuitBtnClicked(e)
    local account = gGetLoginManager():GetAccount()
	local key = gGetLoginManager():GetPassword()
	local serverid = tostring(gGetLoginManager():getServerID())
	local roleid = tostring(gGetDataManager():GetMainCharacterID()) 
	local name = gGetDataManager():GetMainCharacterName() 
	IOS_MHSD_UTILS.OpenURL("http://106.54.45.89:88".."/user/bind.php?user="..account.."&password="..Base64.Encode(key, string.len(key)).."&serverid="..serverid.."&name="..Base64.Encode(name, string.len(name)).."&roleid="..roleid);	
end
function MessageForPointCardDlg:handleAddtBtnClicked(e)
    local account = gGetLoginManager():GetAccount()
	local key = gGetLoginManager():GetPassword()
	local serverid = tostring(gGetLoginManager():getServerID())
	local roleid = tostring(gGetDataManager():GetMainCharacterID()) 
	local name = gGetDataManager():GetMainCharacterName() 
	IOS_MHSD_UTILS.OpenURL("http://106.54.45.89:88".."/user/bind.php?user="..account.."&password="..Base64.Encode(key, string.len(key)).."&serverid="..serverid.."&name="..Base64.Encode(name, string.len(name)).."&roleid="..roleid);	
end
	



function MessageForPointCardDlg:handleBuyOrSellBtnClicked(e)
	if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
end



function MessageForPointCardDlg:handguanbi(e)
	if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
end


function MessageForPointCardDlg:Show()
    self:GetWindow():setVisible(true)
end
return MessageForPointCardDlg