--[[
================================================================================
大唐游戏客户端配置文件
================================================================================
文件名: config.lua
功能: 定义游戏的全局配置参数，包括平台设置、渠道配置等
作者: 大唐游戏开发团队
创建时间: 未知
最后修改: 未知
================================================================================
]]--

-- 全局配置表
Config = {}

-- ============================================================================
-- 第三方平台配置
-- ============================================================================

-- 是否接入第三方平台 (0=否, 1=是)
Config.TRD_PLATFORM = 0 --MT3.ChannelManager:IsTrdPlatform()

-- 当前第三方平台名称
Config.CUR_3RD_PLATFORM = "" --MT3.ChannelManager:GetCur3rdPlatform()

-- 第三方账号登录后缀
Config.CUR_3RD_LOGIN_SUFFIX = "" --MT3.ChannelManager:GetPlatformLoginSuffix()

-- ============================================================================
-- 移动平台配置
-- ============================================================================

-- 是否为Android平台
Config.MOBILE_ANDROID = MT3.ChannelManager:IsAndroid()

-- iOS平台配置
Config.PlatformIOS = "108800100"        -- iOS免费版平台ID
Config.PlatformPointIOS = "108800101"   -- iOS点卡版平台ID

-- ============================================================================
-- 渠道平台ID配置
-- ============================================================================

-- Locojoy平台渠道ID列表
Config.PlatformOfLocojoy = {
    "208800000",  -- 主渠道
    -- 子渠道201-250
    "208890201","208890202","208890203","208890204","208890205",
    "208890206","208890207","208890208","208890209","208890210",
    "208890211","208890212","208890213","208890214","208890215",
    "208890216","208890217","208890218","208890219","208890220",
    "208890221","208890222","208890223","208890224","208890225",
    "208890226","208890227","208890228","208890229","208890230",
    "208890231","208890232","208890233","208890234","208890235",
    "208890236","208890237","208890238","208890239","208890240",
    "208890241","208890242","208890243","208890244","208890245",
    "208890246","208890247","208890248","208890249","208890250",
}

-- 应用宝平台渠道ID列表
Config.PlatformOfYingYongBao = {"208804000","208804001","208804002","208804003","208804004","208804005"}

-- 联想平台渠道ID
Config.PlatformOfLenovo = "208800900"

-- 酷派平台渠道ID
Config.PlatformOfCoolPad = "208802900"

-- 360融合平台渠道ID
Config.PlatformOfRongHe = "208800401"

-- Android通知设置
Config.androidNotifyAll = false
-- ============================================================================
-- 平台判断函数
-- ============================================================================

--[[
判断是否为韩国Android平台
返回值: boolean - true表示是韩国Android平台
]]--
function Config.isKoreanAndroid()
    if Config.CUR_3RD_LOGIN_SUFFIX == "krgp" or Config.CUR_3RD_LOGIN_SUFFIX == "krts" or
       Config.CUR_3RD_LOGIN_SUFFIX == "krnv" or Config.CUR_3RD_LOGIN_SUFFIX == "krlg" then
        return true
    else
        return false
    end
end

--[[
判断是否为台湾地区平台
返回值: boolean - true表示是台湾地区平台
]]--
function Config.isTaiWan()
    if Config.CUR_3RD_LOGIN_SUFFIX == "efis"
    	or Config.CUR_3RD_LOGIN_SUFFIX == "efad"
    	or Config.CUR_3RD_LOGIN_SUFFIX == "lngz"
    	or Config.CUR_3RD_LOGIN_SUFFIX == "tw36"
    	or Config.CUR_3RD_LOGIN_SUFFIX == "twap" then
        return true
    else
        return false
    end
end

--[[
设置当前第三方平台标识
参数: str - 第三方平台名称字符串
]]--
function Config.setCur3rdPlatform(str)
    Config.CUR_3RD_PLATFORM  = str
end

--[[
判断是否为Locojoy平台
返回值: boolean - true表示是Locojoy平台
]]--
function Config.IsLocojoy()
    for _,id in pairs(Config.PlatformOfLocojoy) do
        if gGetChannelName() == id then
            return true
        end
    end
    return false
end

--[[
判断是否为应用宝平台
返回值: boolean - true表示是应用宝平台
]]--
function Config.IsYingYongBao()
    for _,id in pairs(Config.PlatformOfYingYongBao) do
        if gGetChannelName() == id then
            return true
        end
    end
    return false
end

--[[
判断是否为360融合平台
返回值: boolean - true表示是360融合平台
]]--
function Config_IsRongHe()
    if gGetChannelName() == Config.PlatformOfRongHe then
        return true
    end
    return false
end

--[[
Locojoy平台判断的别名函数
返回值: boolean - true表示是Locojoy平台
]]--
function Config_IsLocojoy()
	return Config.IsLocojoy()
end

--[[
获取游戏名称
参数: servertype - 服务器类型 ("0"表示免费服，其他表示点卡服)
返回值: string - 游戏名称
]]--
function Config.GetGameName(servertype)
    local gameName
    if servertype ~= nil then
        if servertype == "0" then
            -- 免费服从通用配置表获取游戏名称
            gameName = GameTable.common.GetCCommonTableInstance():getRecorder(426).value
        else
            -- 点卡服从付费配置表获取游戏名称
            gameName = BeanConfigManager.getInstance():GetTableByName("fushi.ccommondaypay"):getRecorder(426).value
        end
    else
        -- 根据当前服务器类型自动判断
         if  IsPointCardServer() then
            gameName = BeanConfigManager.getInstance():GetTableByName("fushi.ccommondaypay"):getRecorder(426).value
        else
            gameName = GameTable.common.GetCCommonTableInstance():getRecorder(426).value
        end
    end
    return gameName
end

--[[
判断是否为Windows应用程序平台
返回值: boolean - true表示是Windows应用程序平台
]]--
function Config.IsWinApp()
    return Config.CUR_3RD_PLATFORM == "winapp"
end

--[[
在登录前通过读取配置文件判断是否为点卡服
返回值: boolean - true表示是点卡服
]]--
function Config.IsPointCardServerBeforeLogin()
    local inifile = gGetGameApplication():GetIniFileName()
    local isPoint = IniFile:read_profile_int("ClientSetting", "bIsPointVersion", 1, inifile);
    if isPoint > 0 then
        return true
    end
    return false
end

-- 返回配置表
return Config
