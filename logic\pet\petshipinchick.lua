require "logic.dialog"

PetShiPinChick = {}
setmetatable(PetShiPinChick, Dialog)
PetShiPinChick.__index = PetShiPinChick

local _instance
function PetShiPinChick.getInstance()
	if not _instance then
		_instance = PetShiPinChick:new()
		_instance:OnCreate()
	end
	return _instance
end

function PetShiPinChick.getInstanceAndShow()
	if not _instance then
		_instance = PetShiPinChick:new()
		_instance:OnCreate()
	else
		_instance:SetVisible(true)
	end
	return _instance
end

function PetShiPinChick.getInstanceNotCreate()
	return _instance
end

function PetShiPinChick.DestroyDialog()
	if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
end

function PetShiPinChick.ToggleOpenClose()
	if not _instance then
		_instance = PetShiPinChick:new()
		_instance:OnCreate()
	else
		if _instance:IsVisible() then
			_instance:SetVisible(false)
		else
			_instance:SetVisible(true)
		end
	end
end

function PetShiPinChick.GetLayoutFileName()
	return "petshipinchick.layout"
end

function PetShiPinChick:new()
	local self = {}
	self = Dialog:new()
	setmetatable(self, PetShiPinChick)
	return self
end
function PetShiPinChick:setPetKey( petKey )
    self.petKey = petKey
end
function PetShiPinChick:OnCreate()
	Dialog.OnCreate(self)
	local winMgr = CEGUI.WindowManager:getSingleton()
	local needmoney = GameTable.common.GetCCommonTableInstance():getRecorder(486).value
	local growpoint = GameTable.common.GetCCommonTableInstance():getRecorder(493).value
	self.frameWindow = CEGUI.toFrameWindow(winMgr:getWindow("petshipin/framewindow"))
	-- self.quxiaoBtn = CEGUI.toPushButton(winMgr:getWindow("petshipin/quxiao"))
	-- self.querenBtn = CEGUI.toPushButton(winMgr:getWindow("petshipin/queren"))
	
	-- self.describe= winMgr:getWindow("petshipin/textewai")
	-- self.describe:setText(tostring("宠物血脉增加资质各"..growpoint.."点"))
	-- self.needNumText = winMgr:getWindow("petshipin/textqian")
	-- self.needNumText:setText(formatMoneyString(needmoney))
	-- self.quxiaoBtn:subscribeEvent("Clicked", PetShiPinChick.handleUseGoldClicked, self)
	-- self.querenBtn:subscribeEvent("Clicked", PetShiPinChick.handleUseStoneClicked, self)
	 self.frameWindow:getCloseButton():subscribeEvent("Clicked", PetShiPinChick.handleUseGoldClicked, self)
	

end

function PetShiPinChick:handleUseGoldClicked(args)
  PetShiPinChick:DestroyDialog()

end

function PetShiPinChick:handleUseStoneClicked(args)
if self.petKey then
	    if self.petKey >0 then
            local p = require "protodef.fire.pb.pet.cpetshipin".new()
		    p.petkey = self.petKey
		    p.needmoney = GameTable.common.GetCCommonTableInstance():getRecorder(486).value
		    require "manager.luaprotocolmanager":send(p)
        end
    end
    PetShiPinChick.DestroyDialog()
    return true
end

return PetShiPinChick