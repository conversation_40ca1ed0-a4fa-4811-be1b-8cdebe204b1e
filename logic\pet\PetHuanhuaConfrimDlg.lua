require "logic.dialog"

PetHuanhuaConfrimDlg = {}
setmetatable(PetHuanhuaConfrimDlg, Dialog)
PetHuanhuaConfrimDlg.__index = PetHuanhuaConfrimDlg

local _instance
function PetHuanhuaConfrimDlg.getInstance()
	if not _instance then
		_instance = PetHuanhuaConfrimDlg:new()
		_instance:OnCreate()
	end
	return _instance
end

function PetHuanhuaConfrimDlg.getInstanceAndShow()
	if not _instance then
		_instance = PetHuanhuaConfrimDlg:new()
		_instance:OnCreate()
	else
		_instance:SetVisible(true)
	end
	return _instance
end

function PetHuanhuaConfrimDlg.getInstanceNotCreate()
	return _instance
end

function PetHuanhuaConfrimDlg.DestroyDialog()
	if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
end

function PetHuanhuaConfrimDlg.ToggleOpenClose()
	if not _instance then
		_instance = PetHuanhuaConfrimDlg:new()
		_instance:OnCreate()
	else
		if _instance:IsVisible() then
			_instance:SetVisible(false)
		else
			_instance:SetVisible(true)
		end
	end
end

function PetHuanhuaConfrimDlg.GetLayoutFileName()
	return "pethuanhuaqueren.layout"
end

function PetHuanhuaConfrimDlg:new()
	local self = {}
	self = Dialog:new()
	setmetatable(self, PetHuanhuaConfrimDlg)
	return self
end

function PetHuanhuaConfrimDlg:OnCreate()
	Dialog.OnCreate(self)
	local winMgr = CEGUI.WindowManager:getSingleton()
	self.m_OKBtn = CEGUI.toPushButton(winMgr:getWindow("zhuanzhiwuqiqueren/queren"))
	self.m_OKBtn:subscribeEvent("MouseButtonUp", PetHuanhuaConfrimDlg.HandlerOkBtn, self)

	self.m_CancelBtn = CEGUI.toPushButton(winMgr:getWindow("zhuanzhiwuqiqueren/quxiao"))
	self.m_CancelBtn:subscribeEvent("MouseButtonUp", PetHuanhuaConfrimDlg.HandlerCancelBtn, self)

	self.m_TextInfo = CEGUI.toRichEditbox(winMgr:getWindow("zhuanzhiwuqiqueren/miaoshu"))

	self.m_key = 0
	self.m_id = 0

end

function PetHuanhuaConfrimDlg:SetInfoData(selectedPetID , currentPetKey)
	
	self.selectedPetID = selectedPetID
	self.currentPetKey = currentPetKey

	local tip = GameTable.message.GetCMessageTipTableInstance():getRecorder(193008)
	local sb = require "utils.stringbuilder":new()
	local petData = MainPetDataManager.getInstance():FindMyPetByID(currentPetKey)
	local currentPet = BeanConfigManager.getInstance():GetTableByName("pet.cpetattr"):getRecorder(petData.baseid)
	local huanhuaPet = BeanConfigManager.getInstance():GetTableByName("pet.cpetattr"):getRecorder(selectedPetID)
	local freedata = BeanConfigManager.getInstance():GetTableByName("pet.chuanhuatbl"):getRecorder(petData.baseid)
	local text1 = nil
	local text2 = currentPet.name
	if freedata == nil then 
		return
	end
	local huobiText = "金币"
	if freedata.freeCurrencyType == 1 then 
		huobiText = "银币"
	end
	if freedata.freeCurrencyCount > 0 and freedata.freeItemType ~= 0 and freedata.freeItemCount > 0 then 
		text1 = string.format("%d%s和%d个%s碎片",freedata.freeCurrencyCount,huobiText,freedata.freeItemCount,huanhuaPet.name)
	elseif freedata.freeCurrencyCount > 0 then
		text1 = string.format("%d%s",freedata.freeCurrencyCount,huobiText)
	else
		text1 = "异常字符串"
	end
	local text3 = huanhuaPet.name
	sb:Set("parameter1", text1)
	sb:Set("parameter2", text2)
	sb:Set("parameter3", text3)
	local strmsg = sb:GetString(tip.msg)
	sb:delete()
	self.m_TextInfo:AppendParseText(CEGUI.String(strmsg), false)
	self.m_TextInfo:Refresh()
end

function PetHuanhuaConfrimDlg:HandlerOkBtn(e)
	local cmd = require "protodef.fire.pb.pet.cpetputonhuanhua".Create()
	cmd.huanhuaid = self.selectedPetID
	cmd.petkey= self.currentPetKey
	LuaProtocolManager.getInstance():send(cmd)
	PetHuanhuaConfrimDlg.DestroyDialog()
end

function PetHuanhuaConfrimDlg:HandlerCancelBtn(e)
	PetHuanhuaConfrimDlg.DestroyDialog()
end

return PetHuanhuaConfrimDlg