
--[[
================================================================================
日志工具函数库
================================================================================
文件名: log.lua
功能: 提供游戏中的日志记录功能，支持不同级别的日志输出
作者: 大唐游戏开发团队
创建时间: 未知
最后修改: 未知
================================================================================
]]--

--[[
记录错误级别日志
参数: msg - 错误消息字符串
]]--
function LogErr(msg)
	if core.Errors > core.GetCoreLogger():getLoggingLevel() then return end
	core.GetCoreLogger():logLuaEvent(core.Errors,msg)
end

--[[
记录警告级别日志
参数: msg - 警告消息字符串
]]--
function LogWar(msg)
	if core.Warnings > core.GetCoreLogger():getLoggingLevel() then return end
	core.GetCoreLogger():logLuaEvent(core.Warnings,msg)
end

--[[
记录标准级别日志
参数: msg - 标准消息字符串
]]--
function LogStd(msg)
	if core.Standard > core.GetCoreLogger():getLoggingLevel() then return end
	core.GetCoreLogger():logLuaEvent(core.Standard,msg)
end

--[[
记录信息级别日志
参数: msg - 信息消息字符串
]]--
function LogInfo(msg)
	if core.Informative > core.GetCoreLogger():getLoggingLevel() then return end
	core.GetCoreLogger():logLuaEvent(core.Informative,msg)
end

--[[
记录详细级别日志
参数: msg - 详细消息字符串
]]--
function LogInsane(msg)
	if core.Insane> core.GetCoreLogger():getLoggingLevel() then return end
	core.GetCoreLogger():logLuaEvent(core.Insane,msg)
end

--[[
记录自定义级别日志
参数:
  level - 日志级别
  msg - 消息字符串
]]--
function LogCustom(level,msg)
	core.GetCoreLogger():logLuaEventInt(level,msg)
end

--[[
记录通过级别日志
参数:
  level - 日志级别
  msg - 消息字符串
]]--
function LogPass(level,msg)
	core.GetCoreLogger():AddPassLevel(level,"")
	core.GetCoreLogger():logLuaEventInt(level,msg)
end

--[[
格式化输出详细级别日志
参数:
  format - 格式化字符串
  ... - 格式化参数
]]--
function LogInsaneFormat(format, ...)
	local msg = string.format(format, ...)
	print(msg)
end

--[[
记录Flurry统计事件
参数: msg - 事件消息字符串
]]--
function LogFlurryEvent(msg)
	core.Logger:flurryEvent(msg)
end
