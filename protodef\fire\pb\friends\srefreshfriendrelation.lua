require "utils.tableutil"
SRefreshFriendRelation = {}
SRefreshFriendRelation.__index = SRefreshFriendRelation



SRefreshFriendRelation.PROTOCOL_TYPE = 817945

function SRefreshFriendRelation.Create()
	print("enter SRefreshFriendRelation create")
	return SRefreshFriendRelation:new()
end
function SRefreshFriendRelation:new()
	local self = {}
	setmetatable(self, SRefreshFriendRelation)
	self.type = self.PROTOCOL_TYPE
	self.roleid = 0
	self.relation = 0

	return self
end
function SRefreshFriendRelation:encode()
	local os = FireNet.Marshal.OctetsStream:new()
	os:compact_uint32(self.type)
	local pos = self:marshal(nil)
	os:marshal_octets(pos:getdata())
	pos:delete()
	return os
end
function SRefreshFriendRelation:marshal(ostream)
	local _os_ = ostream or FireNet.Marshal.OctetsStream:new()
	_os_:marshal_int64(self.roleid)
	_os_:marshal_short(self.relation)
	return _os_
end

function SRefreshFriendRelation:unmarshal(_os_)
	self.roleid = _os_:unmarshal_int64()
	self.relation = _os_:unmarshal_short()
	return _os_
end

return SRefreshFriendRelation
