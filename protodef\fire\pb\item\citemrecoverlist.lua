require "utils.tableutil"
CItemRecoverList = {}
CItemRecoverList.__index = CItemRecoverList



CItemRecoverList.PROTOCOL_TYPE = 787793

function CItemRecoverList.Create()
	print("enter CItemRecoverList create")
	return CItemRecoverList:new()
end
function CItemRecoverList:new()
	local self = {}
	setmetatable(self, CItemRecoverList)
	self.type = self.PROTOCOL_TYPE
	return self
end
function CItemRecoverList:encode()
	local os = FireNet.Marshal.OctetsStream:new()
	os:compact_uint32(self.type)
	local pos = self:marshal(nil)
	os:marshal_octets(pos:getdata())
	pos:delete()
	return os
end
function CItemRecoverList:marshal(ostream)
	local _os_ = ostream or FireNet.Marshal.OctetsStream:new()
	return _os_
end

function CItemRecoverList:unmarshal(_os_)
	return _os_
end

return CItemRecoverList
