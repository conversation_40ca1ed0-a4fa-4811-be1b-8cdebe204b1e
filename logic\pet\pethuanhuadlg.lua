------------------------------------------------------------------
-- ???????
------------------------------------------------------------------
require "logic.dialog"
require "logic.multimenuset"

local RANDOM_ACT = {
	eActionRun,
	eActionAttack,
	eActionMagic1
}

PetHuanhuaDlg = {
	selectedPetID = 0
}
setmetatable(PetHuanhuaDlg, Dialog)
PetHuanhuaDlg.__index = PetHuanhuaDlg

local _instance
function PetHuanhuaDlg.getInstance()
	if not _instance then
		_instance = PetHuanhuaDlg:new()
		_instance:OnCreate()
	end
	return _instance
end

function PetHuanhuaDlg.getInstanceAndShow()
	if not _instance then
		_instance = PetHuanhuaDlg:new()
		_instance:OnCreate()
	else
		_instance:SetVisible(true)
	end
	return _instance
end

function PetHuanhuaDlg.getInstanceNotCreate()
	return _instance
end

function PetHuanhuaDlg.DestroyDialog()
	if _instance then 
		Dialog.OnClose(_instance)
		if not _instance.m_bCloseIsHide then
			_instance = nil
		end
	end
end

function PetHuanhuaDlg.ToggleOpenClose()
	if not _instance then
		_instance = PetHuanhuaDlg:new()
		_instance:OnCreate()
	else
		if _instance:IsVisible() then
			_instance:SetVisible(false)
		else
			_instance:SetVisible(true)
		end
	end
end

function PetHuanhuaDlg.GetLayoutFileName()
	return "pethuanhuaDlg.layout"
end

function PetHuanhuaDlg:new()
	local self = {}
	self = Dialog:new()
	setmetatable(self, PetHuanhuaDlg)
	return self
end

function PetHuanhuaDlg:OnCreate()
	Dialog.OnCreate(self)
	SetPositionOfWindowWithLabel(self:GetWindow())
	local winMgr = CEGUI.WindowManager:getSingleton()
	self.huanhuaBefore = winMgr:getWindow("petfieldguide_mtg/Left/Item")
	self.profileIcon = winMgr:getWindow("petfieldguide_mtg/Left/Item1")
	self.nameText = winMgr:getWindow("petfieldguide_mtg/Left/Page8")
	self.filterBtn = CEGUI.toGroupButton(winMgr:getWindow("petfieldguide_mtg/left/btnquanbu"))
	self.lingshouBtn = CEGUI.toGroupButton(winMgr:getWindow("petfieldguide_mtg/left/btnquanbu1"))
	self.shenshouBtn = CEGUI.toGroupButton(winMgr:getWindow("petfieldguide_mtg/left/btnquanbu2"))
	self.petScroll = CEGUI.toScrollablePane(winMgr:getWindow("petfieldguide_mtg/left/list"))
	self.petTable = CEGUI.toItemTable(winMgr:getWindow("petfieldguide_mtg/left/list/petTable"))
	self.quedingHuanhua = CEGUI.toPushButton(winMgr:getWindow("Button"))
	self.allPetData = {}
	self.showPropType = 1
	self:loadAllPetData()
	self:refreshPetTable()
	self.quedingHuanhua:subscribeEvent("Clicked", PetHuanhuaDlg.handleIdentifyClicked, self)
	self.filterBtn:subscribeEvent("SelectStateChanged", PetHuanhuaDlg.handleFilterClicked, self)
	self.lingshouBtn:subscribeEvent("SelectStateChanged", PetHuanhuaDlg.handleLingShouClicked, self)
	self.shenshouBtn:subscribeEvent("SelectStateChanged", PetHuanhuaDlg.handleShenShouClicked, self)
	self.filterBtn:setSelected(true)
	self.petScroll:EnableAllChildDrag(self.petScroll)
	self.petTable:subscribeEvent("TableClick", PetHuanhuaDlg.handlePetClicked, self)
end

function PetHuanhuaDlg:converPetShopData()
	self.petShopTable = {}	
	local ids = BeanConfigManager.getInstance():GetTableByName("shop.cpetshop"):getAllID()
	for _,id in pairs(ids) do
		local conf = BeanConfigManager.getInstance():GetTableByName("shop.cpetshop"):getRecorder(id)
		for j=0, conf.goodsids:size()-1 do
			local goods = BeanConfigManager.getInstance():GetTableByName(CheckTableName("shop.cgoods")):getRecorder(conf.goodsids[j])
			self.petShopTable[goods.itemId] = conf.limitLookLv
		end
	end
	ids = nil
end

function PetHuanhuaDlg:loadAllPetData()
	self.allPetData = {}
	local ids = BeanConfigManager.getInstance():GetTableByName("pet.cpetattr"):getAllID()
	for i = 1, #ids do
		local conf = BeanConfigManager.getInstance():GetTableByName("pet.cpetattr"):getRecorder(ids[i])
		if (not IsPointCardServer() and conf.whethershow == 1) or (IsPointCardServer() and conf.pointcardisshow == 1) then
			table.insert(self.allPetData, conf)
		end
	end

	table.sort(self.allPetData, function(v1,v2)
		if v1.uselevel < v2.uselevel then
			return true
		end
		if v1.uselevel == v2.uselevel and v1.id < v2.id then
			return true
		end
		return false
	end)
end

function PetHuanhuaDlg:refreshPetTable(levelmin, levelmax)
	local filterData = {}
	if levelmin == nil or (levelmin == 0 and levelmax == 0) then
		for _,v in pairs(self.allPetData) do
			if v.unusualid~=1 and v.kind ~= fire.pb.pet.PetTypeEnum.SACREDANIMAL then
				table.insert(filterData, v)
			end
		end
    elseif levelmin == 10000 and levelmax == 10000 then --????
        for _,v in pairs(self.allPetData) do
			if  v.unusualid == 1 and v.kind ~= fire.pb.pet.PetTypeEnum.SACREDANIMAL then
				table.insert(filterData, v)
			end
		end
    elseif levelmin == 20000 and levelmax == 20000 then --????
        for _,v in pairs(self.allPetData) do
			if v.kind == fire.pb.pet.PetTypeEnum.SACREDANIMAL then
				table.insert(filterData, v)
			end
		end
	else
		for _,v in pairs(self.allPetData) do
			if  v.uselevel >= levelmin and v.uselevel <= levelmax and v.unusualid~=1 then
				table.insert(filterData, v)
			end
		end
	end
	
	if #filterData > 0 then
		self.selectedPetID = filterData[1].id
	end
	self.selectedPetID = self.selectedPetID or 0
	local num = #filterData
	local row = math.ceil(num/3)
	if self.petTable:GetRowCount() ~= row then
		self.petTable:SetRowCount(row)
		local h = self.petTable:GetCellHeight()
		local spaceY = self.petTable:GetSpaceY()
		self.petTable:setHeight(CEGUI.UDim(0, (h+spaceY)*row))
	end

	for i=1, row*3 do
		local cell = self.petTable:GetCell(i-1)
		cell:Clear()
		cell:SetHaveSelectedState(true)
		if i <= num then
			local conf = filterData[i]
			local shapeData = BeanConfigManager.getInstance():GetTableByName("npc.cnpcshape"):getRecorder(conf.modelid)
			local image = gGetIconManager():GetImageByID(shapeData.littleheadID)
			cell:SetImage(image)
			cell:setID(conf.id)
            SetItemCellBoundColorByQulityPet(cell, conf.quality)
            self.petScroll:EnableChildDrag(cell)
			if conf.id == self.selectedPetID then
				cell:SetSelected(true)
			end
		else
			cell:setVisible(false)
		end
	end	
	self:refreshSelectedPet()
end

function PetHuanhuaDlg:refreshSelectedPet()
	local conf = BeanConfigManager.getInstance():GetTableByName("pet.cpetattr"):getRecorder(self.selectedPetID)
    if not conf then
        return
    end
	self.petConfs = {}
	self.petConfs[conf.kind] = conf
	if conf.kind ~= fire.pb.pet.PetTypeEnum.BABY and conf.thebabyid ~= 0 then --????
		self.petConfs[fire.pb.pet.PetTypeEnum.BABY] = BeanConfigManager.getInstance():GetTableByName("pet.cpetattr"):getRecorder(conf.thebabyid)
	end
	if conf.kind ~= fire.pb.pet.PetTypeEnum.VARIATION and conf.thebianyiid ~= 0 then --????
		self.petConfs[fire.pb.pet.PetTypeEnum.VARIATION] = BeanConfigManager.getInstance():GetTableByName("pet.cpetattr"):getRecorder(conf.thebianyiid)
	end
    if conf.kind ~= fire.pb.pet.PetTypeEnum.WILD and conf.thewildid ~= 0 then --???
        self.petConfs[fire.pb.pet.PetTypeEnum.WILD] = BeanConfigManager.getInstance():GetTableByName("pet.cpetattr"):getRecorder(conf.thewildid)
    end
	
    local wildconf = self.petConfs[fire.pb.pet.PetTypeEnum.WILD] or conf
    local babyconf =  self.petConfs[fire.pb.pet.PetTypeEnum.BABY] or conf
	self.curPetKind = babyconf.kind
	
    self.nameText:setProperty("TextColours", babyconf.colour)
	self.nameText:setText(babyconf.name)
	self:refreshPetSprite(babyconf)
end

function PetHuanhuaDlg:refreshPetSpriteBefore(petConf)
	if petConf == nil then
		return
	end
	if not self.spriteBefore then
		local s = self.huanhuaBefore:getPixelSize()
		self.spriteBefore = gGetGameUIManager():AddWindowSprite(self.huanhuaBefore, petConf.modelid, Nuclear.XPDIR_BOTTOMRIGHT, s.width*0.5, s.height*0.5+50, false)
	else
		self.spriteBefore:SetModel(petConf.modelid)
		self.spriteBefore:SetUIDirection(Nuclear.XPDIR_BOTTOMRIGHT)
	end
    self.spriteBefore:SetDyePartIndex(0,petConf.area1colour)
    self.spriteBefore:SetDyePartIndex(1,petConf.area2colour)
	
	self.elapse1 = 0
	self.defaultActCurTimes1 = 0
	self.defaultActRepeatTimes1 = 3
	self.actType1 = eActionStand
end

function PetHuanhuaDlg:refreshPetSprite(petConf)
	if not self.sprite then
		local s = self.profileIcon:getPixelSize()
		self.sprite = gGetGameUIManager():AddWindowSprite(self.profileIcon, petConf.modelid, Nuclear.XPDIR_BOTTOMRIGHT, s.width*0.5, s.height*0.5+50, false)
	else
		self.sprite:SetModel(petConf.modelid)
		self.sprite:SetUIDirection(Nuclear.XPDIR_BOTTOMRIGHT)
	end
    self.sprite:SetDyePartIndex(0,petConf.area1colour)
    self.sprite:SetDyePartIndex(1,petConf.area2colour)
	
	self.elapse = 0
	self.defaultActCurTimes = 0
	self.defaultActRepeatTimes = 3
	self.actType = eActionStand
end

function PetHuanhuaDlg:update(dt)
	if self.sprite ~= nil then
		self.elapse = self.elapse+dt
		if self.elapse >= self.sprite:GetCurActDuration() then
			self.elapse = 0
			if self.actType == eActionStand then
				self.defaultActCurTimes = self.defaultActCurTimes+1
				if self.defaultActCurTimes == self.defaultActRepeatTimes then
					self.defaultActCurTimes = 0
					local idx = math.random(1, #RANDOM_ACT)
					self.actType = RANDOM_ACT[idx]
					self.sprite:PlayAction(self.actType)
				end
			else
				self.actType = eActionStand
				self.sprite:PlayAction(self.actType)
			end
		end
	end
	
	if self.spriteBefore ~= nil then
		self.elapse1 = self.elapse1+dt
		if self.elapse1 >= self.spriteBefore:GetCurActDuration() then
			self.elapse1 = 0
			if self.actType1 == eActionStand then
				self.defaultActCurTimes1 = self.defaultActCurTimes1+1
				if self.defaultActCurTimes1 == self.defaultActRepeatTimes1 then
					self.defaultActCurTimes1 = 0
					local idx = math.random(1, #RANDOM_ACT)
					self.actType1 = RANDOM_ACT[idx]
					self.spriteBefore:PlayAction(self.actType1)
				end
			else
				self.actType1 = eActionStand
				self.spriteBefore:PlayAction(self.actType1)
			end
		end
	end
end

function PetHuanhuaDlg:handleFilterClicked(args)
	self:refreshPetTable(0, 0)
end

function PetHuanhuaDlg:handleLingShouClicked(args)
	self:refreshPetTable(10000, 10000)

end

function PetHuanhuaDlg:handleShenShouClicked(args)
	self:refreshPetTable(20000, 20000)

end

function PetHuanhuaDlg:handleGroupBtnClicked(args)
	local selectedBtn = self.babyGroupBtn:getSelectedButtonInGroup()
	local kind = selectedBtn:getID()
	self.curPetKind = kind
	local conf = self.petConfs[kind]
    self.nameText:setProperty("TextColours", GetPetNameColour(conf.id))
	self.nameText:setText(conf.name)
	self:refreshPetSprite(conf)
end

function PetHuanhuaDlg:handleIdentifyClicked(args)
	local freedata = BeanConfigManager.getInstance():GetTableByName("pet.chuanhuatbl"):getRecorder(self.selectedPetID)
	local itemNum = RoleItemManager.getInstance():GetItemNumByBaseID(freedata.freeItemType)
	local coinType = fire.pb.game.MoneyType.MoneyType_GoldCoin
	if freedata.freeCurrencyType == 1 then 
		coinType = fire.pb.game.MoneyType.MoneyType_SilverCoin
	end
	if itemNum < freedata.freeItemCount or CurrencyManager.getOwnCurrencyMount(coinType) < freedata.freeCurrencyCount then 
		GetCTipsManager():AddMessageTipById(193010)
		return
	end
	local dlg = require "logic.pet.PetHuanhuaConfrimDlg".getInstanceAndShow()
	if dlg then
		dlg:SetInfoData(self.selectedPetID, self.currentPetKey)
	end
end

function PetHuanhuaDlg:handlePetClicked(args)
	local id = CEGUI.toWindowEventArgs(args).window:getID()
	if self.selectedPetID ~= id then
		self.selectedPetID = id
		self:refreshSelectedPet()
	end
end

function PetHuanhuaDlg:setHuanHuaPet(petKey)
	self.currentPetKey = petKey
	local petData  = MainPetDataManager.getInstance():FindMyPetByID(self.currentPetKey)
	if petData == nil then
		return 
	end
	local currentPet = BeanConfigManager.getInstance():GetTableByName("pet.cpetattr"):getRecorder(petData.baseid)
	self:refreshPetSpriteBefore(currentPet)	
end
return PetHuanhuaDlg
