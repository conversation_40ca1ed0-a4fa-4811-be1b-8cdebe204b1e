require "logic.dialog"

PetEquipTips = {}
setmetatable(PetEquipTips, Dialog)
PetEquipTips.__index = PetEquipTips

local _instance
function PetEquipTips.getInstance()
	if not _instance then
		_instance = PetEquipTips:new()
		_instance:OnCreate()
	end
	return _instance
end

function PetEquipTips.getInstanceAndShow()
	if not _instance then
		_instance = PetEquipTips:new()
		_instance:OnCreate()
	else
		_instance:SetVisible(true)
	end
	return _instance
end

function PetEquipTips.getInstanceNotCreate()
	return _instance
end

function PetEquipTips.DestroyDialog()
	if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
end

function PetEquipTips.ToggleOpenClose()
	if not _instance then
		_instance = PetEquipTips:new()
		_instance:OnCreate()
	else
		if _instance:IsVisible() then
			_instance:SetVisible(false)
		else
			_instance:SetVisible(true)
		end
	end
end

function PetEquipTips.GetLayoutFileName()
	return "petequiptips.layout"
end

function PetEquipTips:new()
	local self = {}
	self = Dialog:new()
	setmetatable(self, PetEquipTips)
	return self
end
function PetEquipTips.closeDialog()
	if not _instance then 
		return
	end
	_instance:DestroyDialog()
	
	
end

function PetEquipTips:OnCreate()
	Dialog.OnCreate(self)
	local winMgr = CEGUI.WindowManager:getSingleton()
	self.itemcell = CEGUI.toItemCell(winMgr:getWindow("petequipinfo/item"))
	self.name = winMgr:getWindow("petequipinfo/names")
	self.jcsz = winMgr:getWindow("petequipinfo/jcsz")
end

--//边界归位
function PetEquipTips:RefreshPosCorrect(nX, nY)
    local mainFrame = self:GetMainFrame()
    local nCorrectX, nCorrectY = require "logic.tips.commontiphelper".RefreshPosCorrect(mainFrame, nX, nY)

    self.nCellPosX = nCorrectX
    self.nCellPosY = nCorrectY
end


function PetEquipTips:makepetequiptips(nItemId,itemkey,itemvalue)
--self:RefreshPosCorrect(650,550)
    local itemAttrCfg = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(nItemId)
    if not itemAttrCfg then
        return false
    end
	
	self.name:setText(itemAttrCfg.name)
    if string.len(itemAttrCfg.colour) > 0 then
        local strNewName = "[colour=" .. "\'" .. itemAttrCfg.colour .. "\'" .. "]" .. itemAttrCfg.name
        self.name:setText(strNewName)
        -- end
    else
        local strNewName = "[colour=" .. "\'" .. "ffffffff" .. "\'" .. "]" .. itemAttrCfg.name
        self.name:setText(strNewName)
    end
	self.itemcell:SetImage(gGetIconManager():GetItemIconByID(itemAttrCfg.icon))
	local item =  BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(id)
	SetItemCellBoundColorByQulityItem(self.itemcell,itemAttrCfg.nquality)
	local propertyCfg = BeanConfigManager.getInstance():GetTableByName("item.cattributedesconfig"):getRecorder(itemkey)
	if itemkey == 1500 then
	itemvalue = itemvalue/20
	end
	
	if propertyCfg ~=nil then
	    strTitleName = propertyCfg.name .. " " .. "+" .. tostring(itemvalue)
		self.jcsz:setText(strTitleName)
	LogInfo("itemkey====="..strTitleName)
	end
	
    
	
	return true



end

return PetEquipTips