require "logic.dialog"

JieBaiDlg = {}
setmetatable(JieBaiDlg, Dialog)
JieBaiDlg.__index = JieBaiDlg

local _instance
function JieBaiDlg.getInstance()
	if not _instance then
		_instance = JieBaiDlg:new()
		_instance:OnCreate()
	end
	return _instance
end

function JieBaiDlg.getInstanceAndShow()
	if not _instance then
		_instance = JieBaiDlg:new()
		_instance:OnCreate()
	else
		_instance:SetVisible(true)
	end
	return _instance
end

function JieBaiDlg.getInstanceNotCreate()
	return _instance
end

function JieBaiDlg.DestroyDialog()
	if _instance then 
        NotificationCenter.removeObserver(Notifi_TeamListChange, JieBaiDlg.handleEventMemberChange)
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
end

function JieBaiDlg.ToggleOpenClose()
	if not _instance then
		_instance = JieBaiDlg:new()
		_instance:OnCreate()
	else
		if _instance:IsVisible() then
			_instance:SetVisible(false)
		else
			_instance:SetVisible(true)
		end
	end
end

function JieBaiDlg.GetLayoutFileName()
	return "jiebaiqueren.layout"
end

function JieBaiDlg:new()
	local self = {}
	self = Dialog:new()
	setmetatable(self, JieBaiDlg)
	return self
end

function JieBaiDlg:OnCreate()
	Dialog.OnCreate(self)
	local winMgr = CEGUI.WindowManager:getSingleton()

    self.teamid= 0
    self.teamInfo = {
        [1] = {},
        [2] = {},
        [3] = {},
        [4] = {},
        [5] = {}
    }

	self.btnEnsure = CEGUI.toPushButton(winMgr:getWindow("jiebai_mtg/oK"))
    self.btnEnsure:subscribeEvent("Clicked", JieBaiDlg.HandleEnsureClicked,self)
	self.btnCancle = CEGUI.toPushButton(winMgr:getWindow("jiebai_mtg/Canle"))
    self.btnCancle:subscribeEvent("Clicked", JieBaiDlg.HandleCancleClicked,self)
	self.title = winMgr:getWindow("jiebai_mtg/biaoti")
	self.name = winMgr:getWindow("jiebai_mtg/jiemiantips1")
    self.ensureText = winMgr:getWindow("jiebai_mtg/yiquerentips")
    self.btnClose = CEGUI.toPushButton(winMgr:getWindow("jiebai_mtg/guanbi"))
    self.btnClose:subscribeEvent("Clicked", JieBaiDlg.HandleCloseClicked,self)
    self.ensureText:setVisible(false)
    self.teamInfo[1].image = winMgr:getWindow("jiebai_mtg/touxiang1/image")
    self.teamInfo[1].stateText = winMgr:getWindow("jiebai_mtg/querenzhuangtai1")
	self.teamInfo[1].name = winMgr:getWindow("jiebai_mtg/juesemingcheng1")
    self.teamInfo[1].state = winMgr:getWindow("jiebai_mtg/touxiang1/querenbiaoshi1")
    self.teamInfo[1].refuse = winMgr:getWindow("jiebai_mtg/touxiang1/jujuebiaoshi1")
    self.teamInfo[1].leave = winMgr:getWindow("jiebai_mtg/touxiang1/zanlibiaoshi1")
    self.teamInfo[1].offLine = winMgr:getWindow("jiebai_mtg/touxiang1/lixianbiaoshi1")
    self.teamInfo[1].school = winMgr:getWindow("jiebai_mtg/touxiang1/zhiyebiaoshi")
    self.teamInfo[1].back = winMgr:getWindow("jiebai_mtg/touxiang1/back")
    self.teamInfo[1].title = winMgr:getWindow("jiebai_mtg/title1/text")

    self.teamInfo[2].image = winMgr:getWindow("jiebai_mtg/touxiang2/image")
    self.teamInfo[2].stateText = winMgr:getWindow("jiebai_mtg/querenzhuangtai2")
    self.teamInfo[2].name = winMgr:getWindow("jiebai_mtg/juesemingcheng2")
    self.teamInfo[2].state = winMgr:getWindow("jiebai_mtg/touxiang1/querenbiaoshi2")
    self.teamInfo[2].refuse = winMgr:getWindow("jiebai_mtg/touxiang1/jujuebiaoshi2")
    self.teamInfo[2].leave = winMgr:getWindow("jiebai_mtg/touxiang1/zanlibiaoshi2")
    self.teamInfo[2].offLine = winMgr:getWindow("jiebai_mtg/touxiang2/lixianbiaoshi2")
    self.teamInfo[2].school = winMgr:getWindow("jiebai_mtg/touxiang2/zhiyebiaoshi2")
    self.teamInfo[2].back = winMgr:getWindow("jiebai_mtg/touxiang2/back")
    self.teamInfo[2].title = winMgr:getWindow("jiebai_mtg/title2/text")

	self.teamInfo[3].image = winMgr:getWindow("jiebai_mtg/touxiang3/image")
	self.teamInfo[3].stateText = winMgr:getWindow("jiebai_mtg/querenzhuangtai3")
    self.teamInfo[3].name = winMgr:getWindow("jiebai_mtg/juesemingcheng3")
	self.teamInfo[3].state = winMgr:getWindow("jiebai_mtg/touxiang1/querenbiaoshi3")
    self.teamInfo[3].refuse = winMgr:getWindow("jiebai_mtg/touxiang1/jujuebiaoshi3") 
    self.teamInfo[3].leave = winMgr:getWindow("jiebai_mtg/touxiang1/zanlibiaoshi3")
    self.teamInfo[3].offLine = winMgr:getWindow("jiebai_mtg/touxiang3/lixianbiaoshi3")
    self.teamInfo[3].school = winMgr:getWindow("jiebai_mtg/touxiang3/zhiyebiaoshi3")
    self.teamInfo[3].back = winMgr:getWindow("jiebai_mtg/touxiang3/back")
    self.teamInfo[3].title = winMgr:getWindow("jiebai_mtg/title3/text")

	self.teamInfo[4].image = winMgr:getWindow("jiebai_mtg/touxiang4/image")
	self.teamInfo[4].stateText = winMgr:getWindow("jiebai_mtg/querenzhuangtai4")
	self.teamInfo[4].name = winMgr:getWindow("jiebai_mtg/juesemingcheng4")
	self.teamInfo[4].state = winMgr:getWindow("jiebai_mtg/touxiang1/querenbiaoshi4")
    self.teamInfo[4].refuse = winMgr:getWindow("jiebai_mtg/touxiang1/jujuebiaoshi4") 
    self.teamInfo[4].leave = winMgr:getWindow("jiebai_mtg/touxiang1/zanlibiaoshi4")
    self.teamInfo[4].offLine = winMgr:getWindow("jiebai_mtg/touxiang4/lixianbiaoshi14")
    self.teamInfo[4].school = winMgr:getWindow("jiebai_mtg/touxiang4/zhiyebiaoshi4")
    self.teamInfo[4].back = winMgr:getWindow("jiebai_mtg/touxiang4/back")
    self.teamInfo[4].title = winMgr:getWindow("jiebai_mtg/title4/text")

	self.teamInfo[5].image = winMgr:getWindow("jiebai_mtg/touxiang5/image")
	self.teamInfo[5].stateText = winMgr:getWindow("jiebai_mtg/querenzhuangtai5")
	self.teamInfo[5].name = winMgr:getWindow("jiebai_mtg/juesemingcheng5")
	self.teamInfo[5].state = winMgr:getWindow("jiebai_mtg/touxiang1/querenbiaoshi5")
    self.teamInfo[5].refuse = winMgr:getWindow("jiebai_mtg/touxiang1/jujuebiaoshi5")
    self.teamInfo[5].leave = winMgr:getWindow("jiebai_mtg/touxiang1/zanlibiaoshi5")
    self.teamInfo[5].offLine = winMgr:getWindow("jiebai_mtg/touxiang5/lixianbiaoshi15")
    self.teamInfo[5].school = winMgr:getWindow("jiebai_mtg/touxiang5/zhiyebiaoshi5")
    self.teamInfo[5].back = winMgr:getWindow("jiebai_mtg/touxiang5/back")
    self.teamInfo[5].title = winMgr:getWindow("jiebai_mtg/title5/text")
    self.nameplaceholder = winMgr:getWindow("jiebai_mtg/biaoti1/text")
    self.m_pNameEdit = CEGUI.toEditbox(winMgr:getWindow("jiebai_mtg/biaoti1/input"))
    self.m_pNameEdit:SetShieldSpace(true)
	self.m_pNameEdit:SetFrameEnabled(false)
    self.m_pNameEdit:subscribeEvent("KeyboardTargetWndChanged", JieBaiDlg.HandleNameKeyboardTargetWndChanged, self)
    NotificationCenter.addObserver(Notifi_TeamListChange, JieBaiDlg.handleEventMemberChange)
    for i = 1, 5 do
        self.teamInfo[i].name:setVisible(false)
        self.teamInfo[i].stateText:setVisible(false)
        self.teamInfo[i].state:setVisible(false)
        self.teamInfo[i].refuse:setVisible(false)
        self.teamInfo[i].leave:setVisible(false)
        self.teamInfo[i].offLine:setVisible(false)
        self.teamInfo[i].back:setVisible(false)
    end
    self:initData()
end
function JieBaiDlg:SetTextVisible()--当成员确认之后关闭后缀输入框
    self.m_pNameEdit:setVisible(false)
end

function JieBaiDlg:HandleNameKeyboardTargetWndChanged(args)--当成员点击输入后缀
    local wnd = CEGUI.toWindowEventArgs(args).window
    if wnd == self.m_pNameEdit then
        self.nameplaceholder:setVisible(false)
    else
        if self.m_pNameEdit:getText() == "" then
            self.nameplaceholder:setVisible(true)
        end
    end
end
function JieBaiDlg:handleEventMemberChange()--队伍人数发生变化，提示
    GetCTipsManager():AddMessageTip(GameTable.message.GetCMessageTipTableInstance():getRecorder(191207).msg)
    local p = require "protodef.fire.pb.npc.cclearjiebaiinfo".new()
	p.teamid = self.teamid
	require "manager.luaprotocolmanager":send(p)
    JieBaiDlg.DestroyDialog()
end

--
function JieBaiDlg:initData()--初始化面板初始化一些基础数据
    local size = GetTeamManager():GetTeamMemberNum()
    local memberInfo
    local Shape
	local iconpath
    for i = 1, size do
        memberInfo = GetTeamManager():GetMember(i)
        Shape = BeanConfigManager.getInstance():GetTableByName("npc.cnpcshape"):getRecorder(memberInfo.shapeID)
	    iconpath = gGetIconManager():GetImagePathByID(Shape.littleheadID)
        local schoolrecord=BeanConfigManager.getInstance():GetTableByName("role.schoolinfo"):getRecorder(memberInfo.eSchool)
        self.teamInfo[i].stateText:setVisible(true)
        self.teamInfo[i].name:setVisible(true)
        self.teamInfo[i].school:setVisible(true)
        self.teamInfo[i].school:setProperty("Image", schoolrecord.schooliconpath)
        self.teamInfo[i].name:setText(memberInfo.strName)
        self.teamInfo[i].image:setProperty("Image", iconpath:c_str())
        self.teamInfo[i].roleId = memberInfo.id
        if gGetDataManager():GetMainCharacterID() == memberInfo.id then
            self.teamInfo[i].back:setVisible(true)
        end
        if GetTeamManager():isAbsentByRoleid(memberInfo.id) == true then
            self.teamInfo[i].leave:setVisible(true)
            self.teamInfo[i].stateText:setVisible(false)
        end
        if GetTeamManager():isOffLineByRoleid(memberInfo.id) == true then
            self.teamInfo[i].offLine:setVisible(true)
            self.teamInfo[i].stateText:setVisible(false)
        end
   end
end
-- ��ʼ�����������ص���Ϣ
function JieBaiDlg:initConnectedData(data)--初始化时设置面板的信息
    self.title:setText(data.name)
    self.name:setText(data.name)
    LogInfo("队伍id"..data.teamid)
    self.teamid=data.teamid
end

function JieBaiDlg:getMemberIndexByID(roleId)
    for i = 1, 5 do
        if self.teamInfo[i].roleId == roleId then
            return i
        end
    end
    return 0
end

function JieBaiDlg:refreshMemberInfo(title,roleId, answer)
    local index = self:getMemberIndexByID(roleId)
    if answer == 1 then -- ͬ��
        self.teamInfo[index].state:setVisible(true)
        self.teamInfo[index].stateText:setVisible(false)
        self.teamInfo[index].refuse:setVisible(false)
        self.teamInfo[index].title:setText(title)
    end
end

function JieBaiDlg:HandleEnsureClicked(args)
    if string.len(self.m_pNameEdit:getText())<1 then
        GetCTipsManager():AddMessageTip(GameTable.message.GetCMessageTipTableInstance():getRecorder(191209).msg)
        return;
    end
    self:sendSelectedInfo(1)
	self.btnEnsure:setVisible(false)
    self.ensureText:setVisible(true)
end

function JieBaiDlg:HandleCancleClicked(args)
    self:sendSelectedInfo(2)
    self.btnEnsure:setVisible(true)
    self.ensureText:setVisible(false)
end

function JieBaiDlg:HandleCloseClicked(args)
    self:sendSelectedInfo(2)
    JieBaiDlg.DestroyDialog()
end
function JieBaiDlg:CloseClicked()
    JieBaiDlg.DestroyDialog()
end

function JieBaiDlg:sendSelectedInfo(answer)
    local p = require("protodef.fire.pb.npc.cjiebaiqueren"):new()
    p.titlename=self.m_pNameEdit:getText()
    p.teamid=self.teamid
    p.answer = answer
	LuaProtocolManager:send(p)
end
return JieBaiDlg