require "logic.dialog"

PetShiPinf = {}
setmetatable(PetShiPinf, Dialog)
PetShiPinf.__index = PetShiPinf

local _instance
function PetShiPinf.getInstance()
	if not _instance then
		_instance = PetShiPinf:new()
		_instance:OnCreate()
	end
	return _instance
end

function PetShiPinf.getInstanceAndShow()
	if not _instance then
		_instance = PetShiPinf:new()
		_instance:OnCreate()
	else
		_instance:SetVisible(true)
	end
	return _instance
end

function PetShiPinf.getInstanceNotCreate()
	return _instance
end

function PetShiPinf.DestroyDialog()
	if _instance then 
		if not _instance.m_bCloseIsHide then
			_instance:OnClose()
			_instance = nil
		else
			_instance:ToggleOpenClose()
		end
	end
end

function PetShiPinf.ToggleOpenClose()
	if not _instance then
		_instance = PetShiPinf:new()
		_instance:OnCreate()
	else
		if _instance:IsVisible() then
			_instance:SetVisible(false)
		else
			_instance:SetVisible(true)
		end
	end
end

function PetShiPinf.GetLayoutFileName()
	return "petshipin.layout"
end

function PetShiPinf:new()
	local self = {}
	self = Dialog:new()
	setmetatable(self, PetShiPinf)
	return self
end
function PetShiPinf:setPetKey( petKey )
    self.petKey = petKey
end
function PetShiPinf:OnCreate()
	Dialog.OnCreate(self)
	local winMgr = CEGUI.WindowManager:getSingleton()
	local ts1 = BeanConfigManager.getInstance():GetTableByName("pet.Ctisheng"):getRecorder(7)
	self.frameWindow = CEGUI.toFrameWindow(winMgr:getWindow("petshipin/framewindow"))
	self.quxiaoBtn = CEGUI.toPushButton(winMgr:getWindow("petshipin/quxiao"))
	self.querenBtn = CEGUI.toPushButton(winMgr:getWindow("petshipin/queren"))
	self.ItemCellNeedItem1 = CEGUI.toItemCell(winMgr:getWindow("petshipin/framewindow/needitem"))
	self.describe= winMgr:getWindow("petshipin/textewai")
	self.describe:setText(tostring("宠物血脉一级增加"..ts1.addnum..ts1.shuoming))
	self.needNumText = winMgr:getWindow("petshipin/textqian")
	self.needNumText:setText(formatMoneyString(ts1.needmoney))
	self.needitemText = winMgr:getWindow("petshipin/textqian1")
	self.itemnum = winMgr:getWindow("petshipin/textqian11")
	local itemInfo = BeanConfigManager.getInstance():GetTableByName("item.citemattr"):getRecorder(ts1.needitemid)
	self.needitemText:setText(ts1.needitemidnum.."个"..itemInfo.name)
	self.ItemCellNeedItem1:SetImage(gGetIconManager():GetItemIconByID(itemInfo.icon))
	local roleItemManager = require("logic.item.roleitemmanager").getInstance()
	local num = roleItemManager:GetItemNumByBaseID(ts1.needitemid)
	self.itemnum:setText(num.."个"..itemInfo.name)
	self.quxiaoBtn:subscribeEvent("Clicked", PetShiPinf.handleUseGoldClicked, self)
	self.querenBtn:subscribeEvent("Clicked", PetShiPinf.handleUseStoneClicked, self)
	self.frameWindow:getCloseButton():subscribeEvent("Clicked", PetShiPinf.handleUseGoldClicked, self)
	

end

function PetShiPinf:handleUseGoldClicked(args)
  PetShiPinf:DestroyDialog()

end

function PetShiPinf:handleUseStoneClicked(args)
if self.petKey then
	    if self.petKey >0 then
            local p = require "protodef.fire.pb.pet.cpetshipin".new()
		    p.petkey = self.petKey
			p.count = 7
		    p.needmoney = GameTable.common.GetCCommonTableInstance():getRecorder(486).value
		    require "manager.luaprotocolmanager":send(p)
        end
    end
    PetShiPinf.DestroyDialog()
    return true
end

return PetShiPinf