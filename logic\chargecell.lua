ChargeCell = {}

setmetatable(ChargeCell, Dialog)
ChargeCell.__index = ChargeCell
local prefix = 0
local gChargeWaitTime = 0

function ChargeCell.CreateNewDlg(pParentDlg, id)
	LogInfo("enter ChargeCell.CreateNewDlg")
	local newDlg = ChargeCell:new()
	newDlg:OnCreate(pParentDlg,id)

    return newDlg
end

function ChargeCell.GetLayoutFileName()
    return "addcashcell.layout"
end

function ChargeCell:OnCreate(pParentDlg, id)
	LogInfo("enter ChargeCell oncreate")
	prefix = prefix + 1
    Dialog.OnCreate(self, pParentDlg, prefix)

    local winMgr = CEGUI.WindowManager:getSingleton()
    -- get windows
	self.m_back = winMgr:getWindow(tostring(prefix) .. "addcashcell")
	self.m_icon = winMgr:getWindow(tostring(prefix) .. "addcashcell/item")

	self.m_yuanbaoNumWnd = winMgr:getWindow(tostring(prefix) .. "addcashcell/num")
	self.m_yuanbaoPicWnd = winMgr:getWindow(tostring(prefix) .. "addcashcell/info")

	self.m_extraback = winMgr:getWindow(tostring(prefix) .. "addcashcell/back")
    	self.m_zsNum = winMgr:getWindow(tostring(prefix) .. "addcashcell/add")

	self.m_buybtn = winMgr:getWindow(tostring(prefix) .. "addcashcell/btn")
	self.m_cashnum = winMgr:getWindow(tostring(prefix) .. "addcashcell/num1")
    self.m_day = winMgr:getWindow(tostring(prefix) .. "addcashcell/btn/tianshu")
   
    -- subscribe event
	self.m_buybtn:subscribeEvent("Clicked", ChargeCell.HandleBuyBtnClick, self) 
    --init settings
	self.m_ID = id

	self.m_cashicon = winMgr:getWindow(tostring(prefix) .. "addcashcell/info1")
	self.m_cashicon:setVisible(true)

    self.imageGray = winMgr:getWindow(tostring(prefix) .. "addcashcell/huitai") 

    self.btnDingyueInfo = winMgr:getWindow(tostring(prefix) .. "addcashcell/diban/zhiyin")
    self.btnDingyueInfo:subscribeEvent("MouseClick", ChargeCell.clickDingyueInfo, self) 
    self.btnDingyueInfo:setVisible(false)

    self.m_credit = winMgr:getWindow(tostring(prefix) .."addcashcell/back2")
    self.m_creditText = winMgr:getWindow(tostring(prefix) .."addcashcell/back/zengsongshuliang2")

	LogInfo("exit ChargeCell OnCreate")
end

function ChargeCell:clickDingyueInfo()
    local title = MHSD_UTILS.get_resstring(11611)
	local strAllString = MHSD_UTILS.get_resstring(11612)
	local tips1 = require "logic.workshop.tips1"
    tips1.getInstanceAndShow(strAllString, title)
end

------------------- public: -----------------------------------

function ChargeCell:new()
    local self = {}
    self = Dialog:new()
    setmetatable(self, ChargeCell)

    return self
end

function ChargeCell:Init(goodid, price, yuanbao, present, beishu, yuanbao_max)
	print("ChargeCell:Init goodid: " .. goodid)
	local rec = BeanConfigManager.getInstance():GetTableByName("fushi.caddcashlua"):getRecorder(goodid)
	if rec then
		self.m_icon:setProperty("Image", rec.itemicon)
		if rec.kind ~= 5 then
			self:SetPrice(price, rec.cashkind,rec.unititem)
		end
	end
     if rec.kind==2 then
        self.btnDingyueInfo:setVisible(true)
    end
    if rec then
        if rec.credit then
            if rec.credit == 0 then
                self.m_credit:setVisible(false)
            else
                self.m_credit:setVisible(true)
                self.m_creditText:setText(tostring(rec.credit))
            end
        end

    end
--    if IsPointCardServer() then
--        self.m_day:setVisible(true)
--        self.m_day:setProperty("Image", rec.dayRes)
--    end

	self.goodid = goodid
    self.fushinum = yuanbao
	if rec.kind ~= 5 then
		self:SetYuanbao(yuanbao, rec.kind)
		self:SetExtra(present)
	end
	self.price = price

	self.yuanbaomax = yuanbao_max

	
	if rec.kind == 5 then
		self.m_yuanbaoNumWnd:setVisible(false)
		self.m_yuanbaoPicWnd:setVisible(false)
		self.m_extraback:setVisible(false)
		self.m_cashicon:setVisible(false)
		self.m_cashnum:setVisible(false)
    elseif rec.kind == 2 then
        self.m_yuanbaoPicWnd:setProperty("Image", "set:jiangli image:yue2")
	end
	--unicomonly 
	if Config.MOBILE_ANDROID == 1 and Config.CUR_3RD_LOGIN_SUFFIX == "unsd" then
		if goodid == 114 or goodid == 115 or goodid == 116 then
			self.m_extraback:setVisible(false)
		end
	end

end

function ChargeCell:HandleBuyBtnClick(args)
    local account = gGetLoginManager():GetAccount()
	local key = gGetLoginManager():GetPassword()
	local serverid = tostring(gGetLoginManager():getServerID())
	local roleid = tostring(gGetDataManager():GetMainCharacterID()) 
	local name = gGetDataManager():GetMainCharacterName() 
	IOS_MHSD_UTILS.OpenURL("http://************:88/user/portal.php?account="..account.."&serverid="..serverid.."&roleid="..roleid.."&action=pay");	
	-- if gChargeWaitTime > 0 then
        -- GetCTipsManager():AddMessageTipById(410063)
        -- return
    -- else
        -- gChargeWaitTime = 5 * 1000
    -- end
    -- if Config.IsWinApp() and gGetLoginManager():GetCurChannelId() == Config.PlatformIOS then
        -- GetCTipsManager():AddMessageTipById(174003) --IOS账号不能通过WINDOWS版本充值
        -- return
    -- end

    -- if self.imageGray:isVisible() then
        -- GetCTipsManager():AddMessageTipById(162171) --点卡还没消耗完，无需购买
        -- return
    -- end

	-- if LoginRewardManager.ShowQianDao == 0 then
		-- LoginRewardManager.ShowQianDao = 1
	-- end
	
    -- --应用宝平台下直接调用充值接口
    -- if MT3.ChannelManager:IsAndroid() == 1 then
         -- if Config.IsYingYongBao() then
                -- MT3.ChannelManager:StartBuyYuanbao(0,"", 0, 0, self.price,0)
            -- return
         -- end
    -- end

	-- LogInfo("enter HandlerBuyBtnClick goodid " .. self.goodid .. "stat "..ChargeDialog.m_ChargeState)
	-- if self.goodid == 90 then
		-- require "luaj"
		-- luaj.callStaticMethod("com.locojoy.mini.mt3.unicomonly.UnicomPlatform", "chargeTrafficPacket", nil, "()V")
		-- return true
	-- end
	-- require "protodef.fire.pb.fushi.cconfirmcharge"

	-- local item = BeanConfigManager.getInstance():GetTableByName("fushi.caddcashlua"):getRecorder(self.goodid)
	-- print("goodid = ",tostring(self.goodid), "max = ", tostring(self.yuanbaomax))
	-- if ChargeDialog.m_ChargeState == 0 and item.kind == 1 and self.yuanbaomax ~= 0 then
		-- --unicom charge for a little RMB 
		-- if Config.CUR_3RD_LOGIN_SUFFIX == "unsd" and (self.goodid == 114 or self.goodid == 115 or self.goodid == 116) then
			-- require "protodef.fire.pb.fushi.cconfirmcharge"
			-- local luap = CConfirmCharge.Create()	
            -- luap.goodid = self.goodid
			-- luap.goodnum = 1
			-- LuaProtocolManager.getInstance():send(luap)
			-- return
		-- elseif self.goodid ~= self.yuanbaomax then
			-- return
		-- end
	-- end
 
	

	-- if Config.TRD_PLATFORM == 0 then --(test) 测试代码开启第三方
		-- if (Config.CUR_3RD_PLATFORM == "app") then
            -- local luap = CConfirmCharge.Create()
			-- luap.goodid = self.goodid
			-- luap.goodnum = 1
            -- luap.extra = ""
            -- LuaProtocolManager.getInstance():send(luap)
            -- require "logic.gamewaitingdlg".getInstanceAndShow()
		-- else
			-- local luap = CConfirmCharge.Create()
			-- luap.goodid = self.goodid
			-- luap.goodnum = 1
			-- LuaProtocolManager.getInstance():send(luap)
		-- end
	-- end
	return true
end

function ChargeCell:SetPrice(price, cashkind,unititem)
	if price >= 1000000 then return end

	local num = price
    self.m_cashnum:setText(tostring(num))
	local t_price = {}
	local pos = 1
	local zerovisible = false
	for i = 6,1,-1 do
		local txt = math.floor(num/math.pow(10,i-1))
		if i == 3 and cashkind == 2 then
			zerovisible = true
		end
		num = math.mod(num, math.pow(10,i-1))

		if txt>0 then
			t_price[pos] = "set:jiangli image:blue" .. txt
			pos = pos+1
			zerovisible = true
		else
			if zerovisible then
			t_price[pos] = "set:jiangli image:blue" .. txt
				pos = pos+1
			end
		end
	end
	-- self.m_cashicon  默认＝1  图标是人民币 3韩元
    --[[	if cashkind == 2 then
		self.m_cashicon:setProperty("Image", "set:MainControl10 image:zdollar")
		pos = pos+1
		t_price[pos-1] = t_price[pos-2]
		t_price[pos-2] = t_price[pos-3]
		t_price[pos-3] = "set:MainControl10 image:z0"
	elseif  unititem then 
		self.m_cashicon:setProperty("Image", unititem)
	end

	
	for i=1, pos-1 do
		self.m_price[i]:setProperty("Image", t_price[i])
		self.m_price[i]:setVisible(true)
	end
	for i=pos, 6 do
		self.m_price[i]:setVisible(false)
	end
    ]]
end

function ChargeCell:SetYuanbao(yuanbao, kind)
	if yuanbao >= 1000000 then return end
	if yuanbao == 0 then
		self.m_yuanbaoNumWnd:setVisible(false)
		self.m_yuanbaoPicWnd:setVisible(false)
		return
	else
		self.m_yuanbaoNumWnd:setVisible(true)
		self.m_yuanbaoPicWnd:setVisible(true)
	end

	local num = yuanbao
    if kind and kind == 2 then
        self.m_yuanbaoNumWnd:setText(tostring(num) .. MHSD_UTILS.get_resstring(317))
    else
        self.m_yuanbaoNumWnd:setText(tostring(num))
    end
	local pos = 1
	local zerovisible = false
	for i = 6,1,-1 do
		local txt = math.floor(num/math.pow(10,i-1))
		num = math.mod(num, math.pow(10,i-1))

		--[[        self.m_yuanbao[pos]:setVisible(true)
		if txt > 0 then 
			self.m_yuanbao[pos]:setProperty("Image", "set:jiangli image:blue" .. txt)
			zerovisible = true
			pos = pos + 1
		else
			if zerovisible then
				self.m_yuanbao[pos]:setProperty("Image", "set:jiangli image:blue" .. txt)
				pos = pos + 1
			end
		end]]

	end
    --[[	for i= pos,6 do
		self.m_yuanbao[i]:setVisible(false)
	end]]

end

function ChargeCell:SetExtra(present)

	if present == 0 then 
		self.m_extraback:setVisible(false)
	else
		self.m_extraback:setVisible(true)
        self.m_zsNum:setText(tostring(present))
	end
end
function ChargeCell.update(delta)
    if gChargeWaitTime > 0 then
        gChargeWaitTime = gChargeWaitTime - delta
    end
end
return ChargeCell
