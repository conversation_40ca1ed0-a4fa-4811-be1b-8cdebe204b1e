require "utils.tableutil"
CReqForceUnlock = {}
CReqForceUnlock.__index = CReqForceUnlock



CReqForceUnlock.PROTOCOL_TYPE = 818936

function CReqForceUnlock.Create()
	print("enter CReq<PERSON><PERSON><PERSON>Unlock create")
	return CReqForceUnlock:new()
end
function CReqForceUnlock:new()
	local self = {}
	setmetatable(self, CReqForceUnlock)
	self.type = self.PROTOCOL_TYPE
	return self
end
function CReqForceUnlock:encode()
	local os = FireNet.Marshal.OctetsStream:new()
	os:compact_uint32(self.type)
	local pos = self:marshal(nil)
	os:marshal_octets(pos:getdata())
	pos:delete()
	return os
end
function CReqForceUnlock:marshal(ostream)
	local _os_ = ostream or FireNet.Marshal.OctetsStream:new()
	return _os_
end

function CReqForceUnlock:unmarshal(_os_)
	return _os_
end

return CReqForceUnlock
